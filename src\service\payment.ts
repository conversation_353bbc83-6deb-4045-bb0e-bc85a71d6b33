import axios from "axios";

interface CreateOrderParams {
  planId: string;
  courseId: string;
  name: string;
  email: string;
  phone: string;
  notes?: Record<string, string>;
}

interface VerifyPaymentParams {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
  courseId: string;
  planId: string;
  name: string;
  email: string;
  phone: string;
  amount: number;
}

export const createPaymentOrder = async (params: CreateOrderParams) => {
  try {
    const response = await axios.post("/api/payment/create-order", params);
    return response.data;
  } catch (error) {
    console.error("Error creating payment order:", error);
    throw error;
  }
};

export const verifyPayment = async (params: VerifyPaymentParams) => {
  try {
    const response = await axios.post("/api/payment/verify", params);
    return response.data;
  } catch (error) {
    console.error("Error verifying payment:", error);
    throw error;
  }
};
