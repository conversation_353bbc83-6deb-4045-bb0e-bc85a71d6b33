"use client";

import { motion } from "framer-motion";
import { scrollReveal } from "@/utils/animations";

interface ScrollRevealProps {
  children: React.ReactNode;
  width?: string;
  delay?: number;
  direction?: "up" | "down" | "left" | "right";
}

const ScrollReveal = ({ 
  children, 
  width = "100%", 
  delay = 0,
  direction = "up"
}: ScrollRevealProps) => {
  // Define different animation variants based on direction
  const getVariants = () => {
    switch (direction) {
      case "down":
        return {
          hidden: { opacity: 0, y: -75 },
          visible: { 
            opacity: 1, 
            y: 0,
            transition: { 
              duration: 0.5,
              delay: delay * 0.1,
              ease: "easeOut" 
            }
          }
        };
      case "left":
        return {
          hidden: { opacity: 0, x: 75 },
          visible: { 
            opacity: 1, 
            x: 0,
            transition: { 
              duration: 0.5,
              delay: delay * 0.1,
              ease: "easeOut" 
            }
          }
        };
      case "right":
        return {
          hidden: { opacity: 0, x: -75 },
          visible: { 
            opacity: 1, 
            x: 0,
            transition: { 
              duration: 0.5,
              delay: delay * 0.1,
              ease: "easeOut" 
            }
          }
        };
      case "up":
      default:
        return {
          hidden: { opacity: 0, y: 75 },
          visible: { 
            opacity: 1, 
            y: 0,
            transition: { 
              duration: 0.5,
              delay: delay * 0.1,
              ease: "easeOut" 
            }
          }
        };
    }
  };

  return (
    <motion.div
      style={{ width }}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      variants={getVariants()}
    >
      {children}
    </motion.div>
  );
};

export default ScrollReveal;
