import { NextResponse } from "next/server";
import { Contact } from "../../../../backend/models";
import { connectDB } from "../../../../backend/lib/mongodb";

export async function GET() {
  try {
    await connectDB();
    const contacts = await Contact.find({}).sort({ createdAt: -1 }).exec();
    return NextResponse.json({ success: true, data: contacts });
  } catch (error: unknown) {
    console.log(error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 500 }
    );
  }
}
