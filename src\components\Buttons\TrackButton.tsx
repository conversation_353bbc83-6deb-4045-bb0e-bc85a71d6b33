import { motion } from "framer-motion";

interface TrackButtonProps {
  comingSoon?: boolean;
  price?: string | number;
  name?: string;
  onclick?: () => void;
}

const TrackButton: React.FC<TrackButtonProps> = ({
  comingSoon = false,
  name = "",
  price,
  onclick
}) => {
  return (
    <div className="mt-auto text-center">
      <motion.button
        className="w-full bg-gradient-to-r from-green-600 to-green-800 hover:from-green-500 hover:to-green-700 text-white font-bold py-3 px-6 rounded-lg shadow-lg disabled:cursor-not-allowed"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        disabled={comingSoon}
        onClick={onclick}
      >
        {comingSoon ? "Coming Soon" : name}
      </motion.button>
    </div>
  );
};

export default TrackButton;
