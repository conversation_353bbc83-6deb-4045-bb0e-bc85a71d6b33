"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import AnimatedButton from "@/components/Common/AnimatedButton";
import Image from "next/image";

interface NotFoundComponentProps {
  title?: string;
  subtitle?: string;
  showHomeButton?: boolean;
  showBackButton?: boolean;
  customMessage?: React.ReactNode;
}

const NotFoundComponent: React.FC<NotFoundComponentProps> = ({
  title = "404 - Page Not Found",
  subtitle = "Oops! The page you're looking for doesn't exist.",
  showHomeButton = true,
  showBackButton = true,
  customMessage,
}) => {
  const router = useRouter();

  return (
    <motion.div 
      className="min-h-screen bg-gradient-to-br from-black to-green-900 flex flex-col items-center justify-center px-4 py-20 text-white"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      <motion.div
        className="absolute inset-0 overflow-hidden z-0 opacity-10"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.1 }}
        transition={{ duration: 1, delay: 0.5 }}
      >
        {/* Grid pattern background */}
        <div className="absolute inset-0 bg-grid-pattern"></div>
      </motion.div>

      <div className="relative z-10 max-w-4xl w-full text-center">
        <motion.div
          className="mb-8"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Image
            src="/assets/images/logo.svg"
            alt="Grow Grid Logo"
            width={150}
            height={150}
            className="mx-auto"
          />
        </motion.div>

        <motion.h1
          className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-green-600"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          {title}
        </motion.h1>

        <motion.div
          className="text-lg sm:text-xl md:text-2xl mb-8 text-gray-300"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          {customMessage || <p>{subtitle}</p>}
        </motion.div>

        <motion.div
          className="flex flex-wrap justify-center gap-6 mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          {showHomeButton && (
            <AnimatedButton
              onClick={() => router.push("/")}
              className="px-8 py-3 text-lg"
              variant="primary"
            >
              Go Home
            </AnimatedButton>
          )}

          {showBackButton && (
            <AnimatedButton
              onClick={() => router.back()}
              className="px-8 py-3 text-lg"
              variant="outline"
            >
              Go Back
            </AnimatedButton>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default NotFoundComponent;
