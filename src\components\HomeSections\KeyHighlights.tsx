import React from "react";
import Header from "../Common/Header";
import {
  FaGraduationCap,
  FaChalkboardTeacher,
  FaLaptopCode,
  FaUsers,
  FaUserTie,
  FaBriefcase,
} from "react-icons/fa";

const KeyHighlights = () => {
  const highlights = [
    {
      id: 1,
      title: "Industry-Approved Curriculum",
      description: "A well-structured, industry-approved curriculum.",
      icon: FaGraduationCap,
      alt: "Curriculum icon",
      img: "/assets/images/keyHighlights/1.png"
    },
    {
      id: 2,
      title: "Expert Faculty",
      description:
        "Live classes conducted by experienced faculty with hands-on expertise.",
      icon: Fa<PERSON>halkboardTeacher,
      alt: "Faculty icon",
      img: "/assets/images/keyHighlights/2.jpeg"
    },
    {
      id: 3,
      title: "Practical Learning",
      description:
        "Immersive practical learning through real-world projects and applications.",
      icon: FaLaptopCode,
      alt: "Projects icon",
      img: "/assets/images/keyHighlights/3.png"
    },
    {
      id: 4,
      title: "Peer Network",
      description:
        "A dynamic peer network spanning diverse batches and backgrounds.",
      icon: FaUsers,
      alt: "Network icon",
      img: "/assets/images/keyHighlights/4.png"
    },
    {
      id: 5,
      title: "1:1 Mentorship",
      description:
        "Personalised 1:1 mentorship from seasoned professionals in the industry.",
      icon: FaUserTie,
      alt: "Mentorship icon",
      img: "/assets/images/keyHighlights/5.png"
    },
    {
      id: 6,
      title: "Career Support",
      description:
        "Comprehensive career support, including mock interviews, profile enhancement, and access to referral networks.",
      icon: FaBriefcase,
      alt: "Career support icon",
      img: "/assets/images/keyHighlights/6.jpg"
    },
  ];

  return (
    <section className="py-5 sm:py-8 md:py-16 bg-gradient-to-br from-green-50 to-green-100">
      <div className="container mx-auto px-3 sm:px-4 flex flex-col gap-3 sm:gap-5">
        <Header boldTitle="Key highlights" title="of Grow Grid program" />

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-8">
          {highlights.map((highlight) => (
            <div
              key={highlight.id}
              className="bg-white rounded-md sm:rounded-lg shadow-md sm:shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 sm:hover:-translate-y-2 hover:scale-[1.02] sm:hover:scale-105 flex flex-col group cursor-pointer"
            >
              <div className="relative h-28 sm:h-36 md:h-48 bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center overflow-hidden">
                {/* <div className="absolute inset-0 bg-white/30 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300" /> */}
                <img
                  src={highlight.img}
                  alt={highlight.alt}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-3 sm:p-4 md:p-6 flex-grow bg-gradient-to-b from-white to-gray-50">
                <h3 className="text-base sm:text-lg md:text-xl font-semibold text-gray-800 mb-1 sm:mb-2 group-hover:text-green-700 transition-colors duration-300">
                  {highlight.title}
                </h3>
                <p className="text-xs sm:text-sm md:text-base text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                  {highlight.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default KeyHighlights;
