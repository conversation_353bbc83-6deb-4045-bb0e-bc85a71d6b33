import TermsScreen from "@/screens/TermsScreen";
import React from "react";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Terms and Conditions | Grow Grid",
  description:
    "Read the terms and conditions for using Grow Grid's educational services and platform.",
  keywords: "terms, conditions, legal, policies, Grow Grid terms",
  openGraph: {
    title: "Terms and Conditions | Grow Grid",
    description: "Important information about using our educational platform",
  },
};

const TermsPage = () => {
  return <TermsScreen />;
};

export default TermsPage;
