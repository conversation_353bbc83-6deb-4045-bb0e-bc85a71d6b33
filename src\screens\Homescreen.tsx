"use client";
import Banner from "@/components/HomeSections/Banner";
import FAQSection from "@/components/HomeSections/FAQSection";
// import EnrollmentProcess from "@/components/HomeSections/EnrollmentProcess";
import Feature from "@/components/HomeSections/Feature";
import GrowthSection from "@/components/HomeSections/Growth";
import InfinityScroll from "@/components/HomeSections/InfinityScroll";
import KeyHighlights from "@/components/HomeSections/KeyHighlights";
import Programs from "@/components/HomeSections/Programs";
import Stats from "@/components/HomeSections/Stats";

export const home_faq_data = [
  {
    question: "What if I can’t keep up with the batch?",
    answer:
      "No worries at all! We get that everyone learns differently. If the pace doesn’t feel right for you, your mentor will help you switch to a batch that better fits your learning style so you get the support and attention you need to thrive.",
  },
  {
    question:
      "Is it like any other course, or do I really get to learn everything from scratch to deployment?",
    answer:
      "Dive into our program and master everything from the fundamentals to cutting-edge concepts, building a rock-solid foundation for your tech career. With hands-on projects and industry-ready skills, you’ll be set to shine. But the journey doesn’t stop there embrace lifelong learning to keep evolving in the ever-changing tech world!",
  },
  {
    question: "What skills will this course help me develop?",
    answer:
      "To build a successful career in technology-based domains, key skills such as analytical thinking, applied mathematics, material engineering, programming, and complex problem-solving are essential. At GROW GRID, we cover all these concepts from the ground up, ensuring that by the end of the course, you will have honed your skills to be fully industry-ready.",
  },
  {
    question:
      "How can I manage time for GROW GRID classes alongside my school/college classes?",
    answer:
      "The GROW GRID program is designed with freshers in mind, offering evening classes to help you balance your academic schedule while enhancing your skills. Plus, if you happen to miss a class, you’ll have access to our LMS (Personal Library), where all sessions are recorded and available for you to review anytime.",
  },
  {
    question: "How GROW GRID Empowers Students?",
    answer:
      "At GROW GRID, our Foundations Programs are designed to shape dynamic, tech-savvy professionals equipped with a deep understanding of their chosen fields. Through a meticulously crafted curriculum, we provide students with a clear, structured path to master essential skills and concepts. Our goal is to empower learners with the confidence and expertise needed to thrive in the ever-evolving tech landscape, setting them up for success in the domains they’re passionate about.",
  },
  {
    question: "What are the Advantages of Online Training & Internship?",
    answer:
      "Online training and internships offer unparalleled flexibility, empowering students to learn at their own pace and on their own terms. Unlike rigid schedules, this format allows learners to engage with lectures and projects when they’re at their peak focus, fostering a more personalized and effective learning experience. This adaptability ensures that students can balance their education with other commitments while building practical skills and real-world expertise in a dynamic, accessible environment.",
  },
];

export default function Homescreen() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <main className="flex flex-col gap-20 pb-20 bg-white">
        {/* Banner Section */}
        <Banner />
        {/* Infinity Scroll Courses Section */}
        <InfinityScroll />
        {/* Key Highlights Section */}
        <KeyHighlights />
        {/* Stats Section */}
        <Stats />
        {/* Growth Section */}
        <GrowthSection />
        {/* Featured Courses Section */}
        <Feature />
        {/* Enrollment Process */}
        {/* <EnrollmentProcess /> */}
        {/* Programs Section */}
        <Programs />

        {/* FAQ Section */}
        <FAQSection faq={home_faq_data} />
      </main>
    </div>
  );
}
