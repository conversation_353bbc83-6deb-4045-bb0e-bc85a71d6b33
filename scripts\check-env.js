#!/usr/bin/env node

/**
 * This script checks if environment variables are properly loaded
 * Run it with: node scripts/check-env.js
 */

// Load environment variables
try {
  require('dotenv').config();
  console.log('Loaded dotenv successfully');
} catch (error) {
  console.warn('Failed to load dotenv:', error.message);
  console.warn('Continuing without dotenv...');
}

// List of important environment variables to check
const envVars = [
  'NODE_ENV', // This is managed by Next.js and should not be set in next.config.ts
  'DB_PRODUCTION',
  'DB_TEST',
  'DB_DEVELOPMENT',
  'DB_LOCAL',
  'JWT_SECRET',
  'NEXT_PUBLIC_API_URL',
];

console.log('=== Environment Variables Check ===');
console.log('Note: NODE_ENV is managed by Next.js and cannot be set in next.config.ts');

// Check each variable
envVars.forEach(varName => {
  const value = process.env[varName];
  if (varName === 'JWT_SECRET') {
    console.log(`${varName}: ${value ? '[SET]' : '[NOT SET]'}`);
  } else {
    console.log(`${varName}: ${value || '[NOT SET]'}`);
  }
});

console.log('=== End Environment Variables Check ===');

// Check if any critical variables are missing
// Note: We exclude NODE_ENV from critical check as it's managed by Next.js
const missingCritical = ['JWT_SECRET'].filter(v => !process.env[v]);

if (missingCritical.length > 0) {
  console.error(`\nWARNING: Missing critical environment variables: ${missingCritical.join(', ')}`);
  console.error('Make sure your .env file is properly set up and loaded.');
  process.exit(1);
} else {
  console.log('\nAll critical environment variables are set.');
  process.exit(0);
}
