import { Schema, model, models } from "mongoose";

const PricingSchema = new Schema(
  {
    currency: {
      type: String,
      default: "INR",
    },
    billingType: {
      type: String,
      enum: ["one-time", "recurring"],
      default: "one-time",
    },
    totalAmount: {
      type: Number,
      required: true,
    },
  },
  { _id: false }
);

const PlanSchema = new Schema(
  {
    planId: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    pricing: {
      type: PricingSchema,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

export default models.Plan || model("Plan", PlanSchema);