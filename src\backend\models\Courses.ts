// models/Course.ts
import { Schema, model, models } from "mongoose";

// Embedded schema: Activity within each Phase
const ActivitySchema = new Schema(
  {
    title: { type: String, trim: true, required: true },
    description: { type: String, trim: true },
  },
  { _id: false }
);

// Embedded schema: Used for both program_goals and program_outcomes
const ProgramGoalSchema = new Schema(
  {
    title: { type: String, trim: true, required: true },
    description: { type: String, trim: true },
  },
  { _id: false }
);

// Embedded schema: Each course phase
const PhaseSchema = new Schema(
  {
    title: { type: String, trim: true, required: true },
    duration: { type: String, trim: true, required: true },
    activities: {
      type: [ActivitySchema],
      default: [],
    },
  },
  { _id: false }
);

// Embedded schema: Tool or resource used in the course
const ResourceSchema = new Schema(
  {
    title: { type: String, trim: true, required: true },
    description: { type: String, trim: true },
    link: { type: String, trim: true },
  },
  { _id: false }
);

// Embedded schema: FAQ item
const FAQSchema = new Schema(
  {
    question: { type: String, trim: true, required: true },
    answer: { type: String, trim: true, required: true },
  },
  { _id: false }
);

// Main Course schema
const CourseSchema = new Schema(
  {
    title: {
      type: String,
      trim: true,
      required: true,
    },
    outcome: {
      type: String,
      trim: true,
      required: true,
    },
    slug: {
      type: String,
      trim: true,
      unique: true,
      required: true,
    },
    duration: {
      type: String,
      trim: true,
    },
    total_projects: {
      type: String,
      default: "0",
    },
    program_goals: {
      type: [ProgramGoalSchema],
      default: [],
    },
    phases: {
      type: [PhaseSchema],
      default: [],
    },
    resources: {
      type: [ResourceSchema],
      default: [],
    },
    program_outcomes: {
      type: [ProgramGoalSchema],
      default: [],
    },
    faq: {
      type: [FAQSchema],
      default: [],
    },
    price: {
      type: String,
      trim: true,
    },
    courseType: {
      type: String,
      enum: ["Internship", "domain"],
      default: "domain",
    },
  },
  {
    timestamps: true,
  }
);

export default models.Course || model("Course", CourseSchema);
