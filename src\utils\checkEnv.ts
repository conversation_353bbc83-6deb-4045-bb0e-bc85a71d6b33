/**
 * Utility to check and debug environment variables
 */

export function checkEnvironmentVariables() {
  console.log('=== Environment Variables Check ===');

  // List of important environment variables to check
  const envVars = [
    'NODE_ENV', // This is managed by Next.js
    'APP_ENV',  // Our custom environment variable that won't be overridden
    'DB_PRODUCTION',
    'DB_TEST',
    'DB_DEVELOPMENT',
    'DB_LOCAL',
    'JWT_SECRET',
    'NEXT_PUBLIC_API_URL',
  ];

  // Check each variable
  envVars.forEach(varName => {
    const value = process.env[varName];
    if (varName === 'JWT_SECRET') {
      console.log(`${varName}: ${value ? '[SET]' : '[NOT SET]'}`);
    } else {
      console.log(`${varName}: ${value || '[NOT SET]'}`);
    }
  });

  console.log('=== End Environment Variables Check ===');

  // Note: NODE_ENV is managed by Next.js and should not be set in next.config.ts
  return {
    // We still return NODE_ENV for informational purposes
    NODE_ENV: process.env.NODE_ENV,
    APP_ENV: process.env.APP_ENV || process.env.NODE_ENV,
    DB_PRODUCTION: process.env.DB_PRODUCTION,
    DB_TEST: process.env.DB_TEST,
    DB_DEVELOPMENT: process.env.DB_DEVELOPMENT,
    DB_LOCAL: process.env.DB_LOCAL,
    JWT_SECRET: process.env.JWT_SECRET,
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  };
}
