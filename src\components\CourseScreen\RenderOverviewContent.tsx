import React from "react";
import {
  FaCheckCircle,
  FaProjectDiagram,
  FaSearch,
  FaTools,
  FaGraduationCap,
  FaHandshake,
} from "react-icons/fa";

interface ProgramGoal {
  id: string;
  title: string;
  description: string;
}

interface ProgramOutcome {
  id: string;
  title: string;
  description: string;
}

interface CourseOverviewProps {
  course?: {
    program_goals?: ProgramGoal[];
    program_outcomes?: ProgramOutcome[];
  };
}

const RenderOverviewContent: React.FC<CourseOverviewProps> = ({ course }) => (
  <>
    {/* Program Goals */}
    <section className="max-w-6xl mx-auto px-6 py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-800">Program Goals</h2>
        <div className="h-1 w-20 bg-green-700 mx-auto my-4"></div>
      </div>
      <div className="grid md:grid-cols-2 gap-8">
        {course?.program_goals?.map((goal, index) => (
          <div
            key={index}
            className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300 transform hover:-translate-y-1"
          >
            <div className="rounded-full bg-green-100 p-3 inline-block mb-4">
              <FaCheckCircle className="text-2xl text-green-700" />
            </div>
            <h3 className="text-xl font-bold mb-2 text-gray-800">
              {goal.title}
            </h3>
            <p className="text-gray-600">{goal.description}</p>
          </div>
        ))}
      </div>
    </section>

    {/* Program Outcomes */}
    <section className="bg-gradient-to-br from-black to-green-900 text-white py-16">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-white">
            Program Outcomes
          </h2>
          <div className="h-1 w-24 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-6"></div>
        </div>
        <div className="grid md:grid-cols-2 gap-8">
          {course?.program_outcomes?.map((outcome, index) => (
            <div
              key={index}
              className="flex items-start p-6 bg-black/30 border border-green-500/30 rounded-lg shadow-lg"
            >
              <div className="mr-4 text-green-400">
                <FaGraduationCap className="text-4xl" />
              </div>
              <div>
                <h3 className="text-xl font-bold mb-2 text-green-400">
                  {outcome.title}
                </h3>
                <p className="text-gray-300">{outcome.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  </>
);

export default RenderOverviewContent;
