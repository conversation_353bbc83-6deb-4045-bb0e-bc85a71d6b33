import { Config } from "@/config";
import Image from "next/image";
import Link from "next/link";

export default function Footer() {
  return (
    <footer className="bg-white text-black py-8 sm:py-10 px-4 sm:px-6">
      <div className="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8">
        {/* Education Section */}
        <div>
          <span className="">
            <Image
              src="/assets/images/logo.svg"
              alt="Learning Banner"
              width={150}
              height={150}
              className="w-auto h-auto max-w-[150px] sm:max-w-[200px]"
            />
          </span>
          <p className="text-gray-400 mt-3"></p>
          <div className="flex space-x-4 mt-4">
            <a href="#" className="text-white text-xl">
              <i className="fab fa-facebook"></i>
            </a>
            <a href="#" className="text-white text-xl">
              <i className="fab fa-instagram"></i>
            </a>
            <a href="#" className="text-white text-xl">
              <i className="fab fa-youtube"></i>
            </a>
            <a href="#" className="text-white text-xl">
              <i className="fab fa-twitter"></i>
            </a>
          </div>
        </div>

        {/* About Section */}
        <div className="mt-6 sm:mt-0">
          <h2 className="text-xl font-semibold">About</h2>
          <ul className="text-gray-400 mt-3 space-y-2">
            <li>
              <Link href="/about">About Us</Link>
            </li>
            {/* <li>
              <Link href="/#features">Features</Link>
            </li>
            <li>
              <Link href="#">News & Blogs</Link>
            </li> */}
            <li>
              <Link href="/contact">Help & Support</Link>
            </li>
          </ul>
        </div>

        {/* Company Section */}
        <div className="mt-6 md:mt-0">
          <h2 className="text-xl font-semibold">Company</h2>
          <ul className="text-gray-400 mt-3 space-y-2">
            <li>
                <Link href="/terms-and-conditions">Terms and conditions</Link>
            </li>
            {/* <li>
              <Link href="/pricing">Pricing</Link>
            </li> */}
            <li>
              <Link href="/refund-policy">Refund policy</Link>
            </li>
            <li>
              <Link href="/privacy-policy">Privacy policy</Link>
            </li>
            {/* <li>
              <Link href="#">FAQ</Link>
            </li> */}
          </ul>
        </div>

        {/* Contact Us Section */}
        <div className="mt-6 md:mt-0">
          <h2 className="text-xl font-semibold">Contact Us</h2>
          <p className="text-gray-400 mt-3">{Config?.ADDRESS}</p>
          <p className="text-gray-400 mt-2">{Config?.CONTACT_NO}</p>
          <p className="text-gray-400 mt-2">{Config?.EMAIL}</p>
          <p className="text-gray-400 mt-2">{Config?.WEBSITE}</p>
        </div>
      </div>
    </footer>
  );
}
