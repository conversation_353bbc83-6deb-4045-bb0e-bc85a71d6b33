import PrivacyPolicyScreen from "@/screens/PrivacyPolicyScreen";
import React from "react";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Privacy Policy | Grow Grid",
  description: "Learn how Grow Grid collects, uses, and protects your personal information.",
  keywords: "privacy policy, data protection, personal information, Grow Grid privacy",
  openGraph: {
    title: "Privacy Policy | Grow Grid",
    description: "How we protect and handle your personal information",
  },
};

const PrivacyPolicyPage = () => {
  return <PrivacyPolicyScreen />;
};

export default PrivacyPolicyPage;
