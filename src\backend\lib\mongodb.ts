import mongoose from "mongoose";
import { env } from "../config/getEnv";
import { checkEnvironmentVariables } from "../../utils/checkEnv";

// Check environment variables and log them
const envVars = checkEnvironmentVariables();

// Try to get the MongoDB URI from multiple sources
let MONGODB_URI = env.DATABASE_URL;

// If not available from env object, try direct process.env access
if (!MONGODB_URI) {
  console.log("DATABASE_URL not found in env object, trying direct process.env access");

  // Use APP_ENV if available, otherwise fall back to NODE_ENV
  const appEnv = process.env.APP_ENV || process.env.NODE_ENV || 'development';
  console.log("Current NODE_ENV:", process.env.NODE_ENV);
  console.log("Current APP_ENV:", appEnv);

  if (appEnv === 'production') {
    MONGODB_URI = process.env.DB_PRODUCTION as string;
    console.log("Using production database");
  } else if (appEnv === 'test') {
    MONGODB_URI = process.env.DB_TEST as string;
    console.log("Using test database");
  } else {
    MONGODB_URI = process.env.DB_DEVELOPMENT as string || process.env.DB_LOCAL as string;
    console.log("Using development database");
  }
}

// Final fallback to local MongoDB
if (!MONGODB_URI) {
  console.warn("No MongoDB URI found in any environment variable, using local fallback");
  MONGODB_URI = "mongodb://localhost:27017/grow-grid-backend";
}

// Debug the MongoDB URI (without showing sensitive information)
if (MONGODB_URI) {
  // Extract just the protocol and hostname for debugging
  try {
    const uriObj = new URL(MONGODB_URI);
    console.log(`MongoDB URI format: ${uriObj.protocol}//${uriObj.hostname}`);

    // Check if the URI contains placeholder text
    if (uriObj.hostname.includes('your-') ||
        uriObj.hostname.includes('example') ||
        uriObj.hostname.includes('placeholder')) {
      console.error("WARNING: MongoDB URI appears to contain placeholder text!");
    }
  } catch (e) {
    console.error("Invalid MongoDB URI format:", MONGODB_URI.substring(0, 10) + "...");
  }
}

console.log("Using MongoDB URI:", MONGODB_URI ? "URI found (not showing for security)" : "No URI found");

const cached = global as unknown as {
  mongoose: {
    conn: null | typeof mongoose;
    promise: null | Promise<typeof mongoose>;
  };
};
if (!cached.mongoose) {
  cached.mongoose = cached.mongoose || { conn: null, promise: null };
}

export async function connectDB() {
  if (cached.mongoose.conn) {
    return cached.mongoose.conn;
  }

  if (!cached.mongoose.promise) {
    const opts = {
      bufferCommands: false,
    };

    console.log("Connecting to MongoDB...", MONGODB_URI);

    cached.mongoose.promise = mongoose
      .connect(MONGODB_URI!, opts)
      .then((mongoose) => {
        return mongoose;
      });
  }

  try {
    cached.mongoose.conn = await cached.mongoose.promise;
    console.log("MongoDB connection successful!");
  } catch (e) {
    cached.mongoose.promise = null;
    console.error("MongoDB connection error:", e instanceof Error ? e.message : String(e));

    // Check for common connection errors
    if (e instanceof Error) {
      if (e.message.includes('ENOTFOUND')) {
        console.error("ENOTFOUND error: The MongoDB hostname could not be found. Check your connection string.");
      } else if (e.message.includes('Authentication failed')) {
        console.error("Authentication error: Check your MongoDB username and password.");
      } else if (e.message.includes('timed out')) {
        console.error("Connection timeout: Check your network or MongoDB Atlas network access settings.");
      }
    }

    throw e;
  }

  return cached.mongoose.conn;
}
