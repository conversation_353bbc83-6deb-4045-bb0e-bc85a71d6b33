"use client";

import React, { useRef, useState } from "react";
import { motion, useMotionValue, useTransform } from "framer-motion";

interface Card3DProps {
  children: React.ReactNode;
  className?: string;
  depth?: number;
  backgroundColor?: string;
}

const Card3D: React.FC<Card3DProps> = ({
  children,
  className = "",
  depth = 30,
  backgroundColor = "white",
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  
  // Motion values for tracking mouse position
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  
  // Transform mouse position to rotation values
  const rotateX = useTransform(mouseY, [-300, 300], [depth, -depth]);
  const rotateY = useTransform(mouseX, [-300, 300], [-depth, depth]);
  
  // Handle mouse move
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!ref.current) return;
    
    const rect = ref.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // Calculate distance from center
    mouseX.set(e.clientX - centerX);
    mouseY.set(e.clientY - centerY);
  };
  
  // Reset on mouse leave
  const handleMouseLeave = () => {
    mouseX.set(0);
    mouseY.set(0);
    setIsHovered(false);
  };

  return (
    <motion.div
      ref={ref}
      className={`relative overflow-hidden ${className}`}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      style={{
        rotateX: isHovered ? rotateX : 0,
        rotateY: isHovered ? rotateY : 0,
        transformStyle: "preserve-3d",
        perspective: 1000,
      }}
      whileHover={{ scale: 1.02 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      {/* Card content */}
      <div className="relative z-10 transform-gpu">
        {children}
      </div>
      
      {/* Lighting effect */}
      {isHovered && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-tr from-transparent via-white to-transparent opacity-20 pointer-events-none"
          style={{
            rotateX: rotateX,
            rotateY: rotateY,
            scale: 1.5,
          }}
        />
      )}
      
      {/* Shadow effect */}
      <motion.div
        className="absolute inset-0 rounded-lg"
        style={{
          boxShadow: isHovered 
            ? "0 20px 40px rgba(0,0,0,0.2)" 
            : "0 10px 20px rgba(0,0,0,0.1)",
          backgroundColor,
          zIndex: -1,
          transformStyle: "preserve-3d",
          transform: "translateZ(-20px)",
        }}
      />
    </motion.div>
  );
};

export default Card3D;
