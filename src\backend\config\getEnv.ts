interface EnvVariables {
  NODE_ENV: "development" | "production" | "test";
  PORT: number;
  DATABASE_URL: string;
  JWT_SECRET: string;
  RAZORPAY_KEY_ID: string;
  RAZORPAY_KEY_SECRET: string;
  RAZORPAY_WEBHOOK_SECRET?: string; // Optional, used for webhook verification
}

const DB_URLS = {
  production: process.env.DB_PRODUCTION,
  test: process.env.DB_TEST,
  development: process.env.DB_DEVELOPMENT,
  default: process.env.DB_LOCAL,
} as const;

const JWT_SECRET = process.env.JWT_SECRET || "default_jwt_secret";

export function getEnv(): EnvVariables {
  if (typeof window !== "undefined") {
    throw new Error("This function should only be called on the server side");
  }

  // Use APP_ENV if available, otherwise fall back to NODE_ENV
  // This allows you to set APP_ENV=production in your .env file
  // even when running in development mode
  const appEnv = process.env.APP_ENV || process.env.NODE_ENV || "development";
  const env = process.env.NODE_ENV || "development";

  //payment gateway keys
  const RAZORPAY_KEY_ID = process.env.RAZORPAY_KEY_ID || "default_key_id";
  const RAZORPAY_KEY_SECRET = process.env.RAZORPAY_KEY_SECRET || "default_key_secret";
  const RAZORPAY_WEBHOOK_SECRET = process.env.RAZORPAY_WEBHOOK_SECRET || "";
  
  // Use appEnv for database selection instead of NODE_ENV
  const databaseUrl = DB_URLS[appEnv as keyof typeof DB_URLS] || DB_URLS.default;

  if (!JWT_SECRET) {
    console.warn("JWT_SECRET environment variable is not set, using default");
  }

  const result: EnvVariables = {
    NODE_ENV: env as EnvVariables["NODE_ENV"],
    PORT: parseInt(process.env.PORT || "8000", 10),
    DATABASE_URL: databaseUrl || "mongodb://localhost:27017/grow-grid-backend",
    JWT_SECRET,
    RAZORPAY_KEY_ID,
    RAZORPAY_KEY_SECRET,
    RAZORPAY_WEBHOOK_SECRET,
  };

  // Removed cleanup code as it can cause issues if env vars are needed elsewhere
  // Don't delete environment variables as they might be needed by other parts of the application

  return result;
}

// Export a server-side singleton instance
export const env = getEnv();
