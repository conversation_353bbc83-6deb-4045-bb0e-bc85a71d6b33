import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { FaGraduationCap, FaClock, FaLaptop } from "react-icons/fa";
import AnimatedButton from "@/components/Common/AnimatedButton";

const ComingSoonSection = () => {
  const [displayText, setDisplayText] = useState("");
  const displayDescription = `We're working hard to bring you an amazing learning experience.
          This domain will be available soon!`;

  // Typewriter effect for "Coming Soon" text
  useEffect(() => {
    const text = "Coming Soon";
    let currentIndex = 0;
    let isDeleting = false;
    let typingSpeed = 150; // Speed for typing
    let deletingSpeed = 75; // Speed for deleting
    let pauseTime = 1500; // Pause time at full text

    const typeEffect = () => {
      // Current text state
      const fullText = text;

      // If deleting
      if (isDeleting) {
        // Remove a character
        setDisplayText(fullText.substring(0, currentIndex - 1));
        currentIndex--;
        typingSpeed = deletingSpeed;
      } else {
        // Add a character
        setDisplayText(fullText.substring(0, currentIndex + 1));
        currentIndex++;
        typingSpeed = 150;
      }

      // Transition between typing and deleting
      if (!isDeleting && currentIndex === fullText.length) {
        // Finished typing, pause before deleting
        typingSpeed = pauseTime;
        isDeleting = true;
      } else if (isDeleting && currentIndex === 0) {
        // Finished deleting, start typing again
        isDeleting = false;
        typingSpeed = 500; // Pause before typing again
      }

      // Schedule next update
      setTimeout(typeEffect, typingSpeed);
    };

    // Start the effect
    const timerId = setTimeout(typeEffect, 500);

    // Cleanup
    return () => clearTimeout(timerId);
  }, []);

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-black to-green-900 flex flex-col items-center justify-center px-4 py-20 text-white"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      <motion.div
        className="absolute inset-0 overflow-hidden z-0 opacity-10"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.1 }}
        transition={{ duration: 1, delay: 0.5 }}
      >
        <div className="absolute inset-0 bg-[url('/api/placeholder/1200/400')] bg-cover bg-center"></div>
      </motion.div>

      <div className="relative z-10 max-w-4xl mx-auto text-center">
        <motion.div
          className="bg-green-500 text-black p-4 rounded-full mx-auto mb-8 w-24 h-24 flex items-center justify-center"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{
            type: "spring",
            stiffness: 200,
            damping: 15,
            delay: 0.2,
          }}
        >
          <FaGraduationCap className="text-4xl" />
        </motion.div>

        <motion.h1
          className="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-white">
            {displayText}
            <span className="animate-pulse font-medium">|</span>
          </span>
        </motion.h1>

        <motion.div
          className="h-1 w-24 bg-green-500 mx-auto my-8"
          initial={{ width: 0 }}
          animate={{ width: "6rem" }}
          transition={{ duration: 1, delay: 0.6 }}
        ></motion.div>

        <motion.p
          className="text-xl md:text-2xl text-gray-300 mb-10 max-w-2xl mx-auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          {displayDescription}
        </motion.p>

        <motion.div
          className="flex flex-wrap justify-center gap-6 mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
        >
          <div className="bg-black/30 backdrop-blur-sm px-6 py-4 rounded-lg border border-green-500/30 flex items-center">
            <FaClock className="text-2xl mr-3 text-green-400" />
            <span className="font-semibold">Coming Soon</span>
          </div>

          <div className="bg-black/30 backdrop-blur-sm px-6 py-4 rounded-lg border border-green-500/30 flex items-center">
            <FaLaptop className="text-2xl mr-3 text-green-400" />
            <span className="font-semibold">Stay Tuned</span>
          </div>
        </motion.div>

        <motion.div
          className="mt-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2 }}
        >
          <AnimatedButton
            onClick={() => window.history.back()}
            className="px-8 py-3 text-lg"
            variant="primary"
          >
            Go Back
          </AnimatedButton>
        </motion.div>
      </div>
    </motion.div>
  );
};
export default ComingSoonSection;
