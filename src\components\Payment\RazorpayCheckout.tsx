"use client";
import React, { useState } from "react";
import { createPaymentOrder, verifyPayment } from "@/service/payment";
import { motion } from "framer-motion";
import { FaSpinner } from "react-icons/fa";
import { toast } from "react-hot-toast";

// Define Razorpay window interface
declare global {
  interface Window {
    Razorpay: any;
  }
}

interface RazorpayCheckoutProps {
  id: string;
  amount: number;
  courseId: string;
  courseName: string;
  name: string;
  email: string;
  phone: string;
  planId: string;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  onClose?: () => void;
}

const RazorpayCheckout: React.FC<RazorpayCheckoutProps> = ({
  amount,
  courseId,
  courseName,
  name,
  email,
  phone,
  onSuccess,
  onError,
  onClose,
  planId
}) => {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const loadRazorpayScript = () => {
    return new Promise<void>((resolve, reject) => {
      const script = document.createElement("script");
      script.src = "https://checkout.razorpay.com/v1/checkout.js";
      script.onload = () => resolve();
      script.onerror = () => reject(new Error("Failed to load Razorpay SDK"));
      document.body.appendChild(script);
    });
  };

  const handlePayment = async () => {
    try {
      setLoading(true);
      setErrorMessage(null);

      // Load Razorpay script if not already loaded
      if (typeof window !== "undefined" && !window.Razorpay) {
        await loadRazorpayScript();
      }

      // Create order
      const orderData = await createPaymentOrder({
        planId,
        courseId,
        name,
        email,
        phone,
        notes: {
          courseName,
        },
      });

      if (!orderData.success) {
        throw new Error(orderData.message || "Failed to create order");
      }

      // Verify amount matches plan price
      if (amount !== orderData.planDetails.amount) {
        throw new Error(
          `Payment amount does not match plan price`
        );
      }

      // Configure Razorpay options
      const options = {
        key: orderData.key,
        amount: orderData.order.amount,
        currency: orderData.order.currency,
        name: "Grow Grid",
        description: `Payment for ${courseName}`,
        order_id: orderData.order.id,
        prefill: {
          name: name,
          email: email,
          contact: phone,
        },
        notes: {
          courseId,
        },
        theme: {
          color: "#16a34a",
        },
        handler: async function (response: any) {
          try {
            // Verify payment
            const verificationData = await verifyPayment({
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              courseId,
              name,
              email,
              phone,
              amount,
              planId
            });

            if (verificationData.success) {
              toast.success("Payment successful!");
              if (onSuccess) {
                onSuccess(verificationData);
              }
            } else {
              throw new Error(verificationData.message || "Payment verification failed");
            }
          } catch (error: any) {
            const errorMsg = error.response?.data?.message || error.message || "Payment verification failed";
            setErrorMessage(errorMsg);
            toast.error(errorMsg);
            if (onError) {
              onError(error);
            }
            // Close modal after error
            if (onClose) {
              setTimeout(() => onClose(), 3000);
            }
          } finally {
            setLoading(false);
          }
        },
        modal: {
          ondismiss: function () {
            setLoading(false);
            if (onClose) {
              onClose();
            }
          },
        },
      };

      // Initialize Razorpay
      const razorpay = new window.Razorpay(options);
      razorpay.open();
    } catch (error: any) {
      console.error("Payment initialization error:", error);
      const errorMsg = error.response?.data?.message || error.message || "Payment initialization failed";
      setErrorMessage(errorMsg);
      toast.error(errorMsg);
      setLoading(false);
      if (onError) {
        onError(error);
      }
      // Close modal after error
      if (onClose) {
        setTimeout(() => onClose(), 3000);
      }
    }
  };

  return (
    <div className="flex flex-col">
      {errorMessage && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {errorMessage}
        </div>
      )}
      <motion.button
        className="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg shadow-lg flex items-center justify-center"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={handlePayment}
        disabled={loading}
      >
        {loading ? (
          <>
            <FaSpinner className="animate-spin mr-2" />
            Processing...
          </>
        ) : (
          `Pay ₹${amount}`
        )}
      </motion.button>
    </div>
  );
};

export default RazorpayCheckout;


