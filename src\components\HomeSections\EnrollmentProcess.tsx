// EnrollmentProcess.js
import Image from "next/image";
import React from "react";

const EnrollmentProcess = () => {
  const steps = [
    { number: "01", title: "Choose a Program" },
    { number: "02", title: "Enroll and Submit Documents" },
    { number: "03", title: "Choose a Date and Time" },
    { number: "04", title: "Pick an Instructor" },
    { number: "05", title: "Then Start" },
  ];

  return (
    <div className="bg-black text-white p-4 md:p-10 flex flex-col md:flex-row justify-between relative">
      <div className="hidden md:block">
        <Image
          src="/assets/images/tempImageHome2.png"
          alt="Enrollment Temp Image"
          width={350}
          height={350}
          className="absolute bottom-0 z-10"
        />
      </div>
      <div className="w-full md:max-w-3xl">
        <h1 className="text-2xl md:text-4xl font-bold mb-2 md:mb-3">Effortless Enrollment</h1>
        <p className="text-gray-300 mb-6 md:mb-8 text-sm md:text-base">
          Lorem ipsum is simply dummy text of the printing and typesetting
          industry....
        </p>

        <div className="relative">
          {steps.map((step, index) => (
            <div
              key={step.number}
              className={`relative ${index < steps.length - 1 && "mb-4 md:mb-8"}`}
            >
              <div className="bg-white text-black rounded-lg px-3 md:px-4 py-3 md:py-4 flex items-center">
                <div className="font-bold text-xl md:text-2xl mr-2 border-r-2 w-10 md:w-12">
                  {step.number}
                </div>
                <div className="text-base md:text-lg font-semibold">{step.title}</div>
              </div>

              {index < steps.length - 1 && (
                <div className="absolute right-4 bottom-0 w-12 md:w-16 h-12 md:h-16 border-r-2 border-dashed border-gray-400 rounded-br-3xl transform translate-y-1/2"></div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EnrollmentProcess;
