import LiveTrackContent from "./Tracks/LiveTrackContent";
import SelfTrackContent from "./Tracks/SelfTrackContent";
import { motion } from "framer-motion";

interface TracksProps {
  setEnrollmentOpen: React.Dispatch<React.SetStateAction<boolean>>;
  ref?: React.RefObject<HTMLDivElement>
}

const Tracks = ({ setEnrollmentOpen, ref }: TracksProps) => {
  return (
    <div ref={ref} className="max-w-6xl mx-auto px-4 sm:px-6 py-12">
      <div className="text-center mb-12">
        <h2 className="text-3xl sm:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-700 to-black">
          Choose Your Learning Path
        </h2>
        <div className="h-1 w-24 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-6"></div>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto">
          Select the training option that best fits your learning style and
          schedule
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <motion.div
          className="bg-gradient-to-br from-black to-green-900 rounded-xl overflow-hidden shadow-xl border border-green-500/30 h-full"
          whileHover={{
            scale: 1.02,
            boxShadow:
              "0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1)",
            borderColor: "rgba(74, 222, 128, 0.4)",
          }}
          transition={{ type: "spring", stiffness: 300, damping: 15 }}
        >
          <LiveTrackContent
            setEnrollmentOpen={setEnrollmentOpen}
          />
        </motion.div>

        <motion.div
          className="bg-gradient-to-br from-black to-green-900 rounded-xl overflow-hidden shadow-xl border border-green-500/30 h-full"
          whileHover={{
            scale: 1.02,
            boxShadow:
              "0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1)",
            borderColor: "rgba(74, 222, 128, 0.4)",
          }}
          transition={{ type: "spring", stiffness: 300, damping: 15 }}
        >
          <SelfTrackContent
            setEnrollmentOpen={setEnrollmentOpen}
          />
        </motion.div>
      </div>
    </div>
  );
};

export default Tracks;
