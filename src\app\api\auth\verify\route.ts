import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import { env } from "../../../../backend/config/getEnv";

const JWT_SECRET = env.JWT_SECRET;

export async function POST(req: NextRequest) {
  try {
    // Get the token from body
    const body = await req.json();
    const token = body.token;

    if (!token) {
      return NextResponse.json(
        { message: "No token provided" },
        { status: 401 }
      );
    }

    // Verify the token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Ensure the decoded token contains the role
    if (!decoded || typeof decoded !== "object" || !("role" in decoded)) {
      return NextResponse.json(
        { message: "Invalid token format" },
        { status: 401 }
      );
    }

    // Check if admin access is required but user is not admin
    if (decoded.role !== "admin") {
      return NextResponse.json(
        { message: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Return the decoded user information
    return NextResponse.json(
      {
        message: "Token is valid",
        user: decoded,
      },
      { status: 200 }
    );
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json({ message: "Invalid token" }, { status: 401 });
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
