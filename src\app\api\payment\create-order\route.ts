import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON><PERSON><PERSON> from "razorpay";
import { z } from "zod";
import { connectDB } from "../../../../backend/lib/mongodb";
import { env } from "../../../../backend/config/getEnv";
import Plan from "../../../../backend/models/Plan";

// Get Razorpay credentials from environment variables
const RAZORPAY_KEY_ID = env.RAZORPAY_KEY_ID;
const RAZORPAY_KEY_SECRET = env.RAZORPAY_KEY_SECRET;

// Validation schema for order creation
const orderSchema = z.object({
  planId: z.string().min(1, "Plan ID is required"),
  courseId: z.string(),
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email format"),
  phone: z.string().min(10, "Phone number must be at least 10 characters"),
  notes: z.record(z.string()).optional(),
});

export async function POST(req: NextRequest) {
  try {
    // Check if Razorpay credentials are configured
    if (!RAZORPAY_KEY_ID || !RAZORPAY_KEY_SECRET) {
      return NextResponse.json(
        { success: false, message: "Payment gateway not configured" },
        { status: 500 }
      );
    }

    await connectDB();
    const body = await req.json();
    
    // Validate request body
    const validationResult = orderSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Validation error", 
          errors: validationResult.error.errors 
        },
        { status: 400 }
      );
    }
    
    const { planId, courseId, name, email, phone, notes } = validationResult.data;

    // Fetch plan details from database
    const plan = await Plan.findOne({ planId, isActive: true });
    if (!plan) {
      return NextResponse.json(
        { success: false, message: "Invalid or inactive plan" },
        { status: 400 }
      );
    }

    // Get amount and currency from the plan
    const amount = plan.pricing.totalAmount;
    const currency = plan.pricing.currency;

    // Initialize Razorpay
    const razorpay = new Razorpay({
      key_id: RAZORPAY_KEY_ID,
      key_secret: RAZORPAY_KEY_SECRET,
    });

    // Create order (amount in paise)
    const order = await razorpay.orders.create({
      amount: amount * 100,
      currency: currency,
      receipt: `receipt_${Date.now()}`,
      notes: {
        planId,
        courseId,
        name,
        email,
        phone,
        ...notes,
      },
    });

    return NextResponse.json(
      { 
        success: true, 
        order,
        key: RAZORPAY_KEY_ID,
        planDetails: {
          name: plan.name,
          amount,
          currency
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error creating payment order:", error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Failed to create payment order" 
      },
      { status: 500 }
    );
  }
}



