"use client";

import React, { useRef, useState, useEffect } from "react";
import { motion, PanInfo, useAnimation } from "framer-motion";

interface DraggableCarouselProps {
  children: React.ReactNode[];
  className?: string;
  itemWidth?: number;
  gap?: number;
  sensitivity?: number;
  showDots?: boolean;
  autoPlay?: boolean;
  interval?: number;
}

const DraggableCarousel: React.FC<DraggableCarouselProps> = ({
  children,
  className = "",
  itemWidth = 300,
  gap = 20,
  sensitivity = 0.5,
  showDots = true,
  autoPlay = false,
  interval = 3000,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [constraintWidth, setConstraintWidth] = useState(0);
  const carouselRef = useRef<HTMLDivElement>(null);
  const controls = useAnimation();
  
  // Calculate the total width of the carousel
  useEffect(() => {
    if (carouselRef.current) {
      const containerWidth = carouselRef.current.clientWidth;
      const totalItems = React.Children.count(children);
      const totalWidth = totalItems * (itemWidth + gap) - gap;
      setConstraintWidth(Math.max(totalWidth - containerWidth, 0));
    }
  }, [children, itemWidth, gap]);
  
  // Auto play functionality
  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (autoPlay && children.length > 1) {
      timer = setInterval(() => {
        const nextIndex = (activeIndex + 1) % children.length;
        handleDotClick(nextIndex);
      }, interval);
    }
    
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [autoPlay, activeIndex, children.length, interval]);
  
  // Handle drag end
  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const velocity = info.velocity.x * sensitivity;
    const direction = velocity < 0 ? 1 : -1;
    
    // If the velocity is high enough, move to the next/prev item
    if (Math.abs(velocity) > 500) {
      const nextIndex = Math.max(0, Math.min(activeIndex + direction, children.length - 1));
      handleDotClick(nextIndex);
    }
  };
  
  // Handle dot click
  const handleDotClick = (index: number) => {
    setActiveIndex(index);
    
    // Calculate the position to scroll to
    const position = index * -(itemWidth + gap);
    const clampedPosition = Math.max(-constraintWidth, Math.min(0, position));
    
    controls.start({
      x: clampedPosition,
      transition: { type: "spring", stiffness: 300, damping: 30 }
    });
  };

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <motion.div
        ref={carouselRef}
        className="flex items-center"
        drag="x"
        dragConstraints={{ left: -constraintWidth, right: 0 }}
        dragElastic={0.1}
        onDragEnd={handleDragEnd}
        animate={controls}
      >
        {React.Children.map(children, (child, index) => (
          <motion.div
            key={index}
            className="flex-shrink-0"
            style={{ 
              width: itemWidth, 
              marginRight: index < children.length - 1 ? gap : 0 
            }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            {child}
          </motion.div>
        ))}
      </motion.div>
      
      {/* Navigation dots */}
      {showDots && children.length > 1 && (
        <div className="flex justify-center mt-4 space-x-2">
          {children.map((_, index) => (
            <motion.button
              key={index}
              className={`w-3 h-3 rounded-full ${
                index === activeIndex ? "bg-green-600" : "bg-gray-300"
              }`}
              onClick={() => handleDotClick(index)}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default DraggableCarousel;
