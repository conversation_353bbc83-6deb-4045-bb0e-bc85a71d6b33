"use client";
import { useRef, useState, useEffect } from "react";
import Header from "../Common/Header";
import InfoCard from "../Common/InfoCard";
import {
  FaChevronLeft,
  FaChevronRight,
  FaUserGraduate,
  FaTools,
  FaRobot,
  FaChalkboardTeacher,
  FaGamepad,
  FaMobileAlt,
  FaCertificate
} from "react-icons/fa";
import { motion } from "framer-motion";
import { staggerContainer } from "@/utils/animations";

const tempData = [
  {
    title: "Personalised Learning",
    description:
      "Adaptive courses tailored to your learning pace and styleCourses that adapt to your pace and style because you set the rules.",
  },
  {
    title: "Skill-Based Curriculum",
    description:
      "Courses that go beyond textbooks, giving you real-world skills that actually matter.",
  },
  {
    title: "AI-Powered Insights",
    description:
      "Smart analytics that track your progress, crush your goals, and supercharge your performance.",
  },
  {
    title: "Expert-Led Sessions",
    description:
      "Learn from the best—top educators, mentors, and industry pros who know what’s up.",
  },
  {
    title: "Interactive & Gamified Learning",
    description:
      "Quizzes, challenges, and modules that make learning a game you’ll want to win.",
  },
  {
    title: "Learn Anytime, Anywhere",
    description:
      "Access your lessons on mobile, tablet, or desktop because learning should never be limited.",
  },
  {
    title: "Career & Certification Support",
    description:
      "Earn your certifications and get the insider guidance you need to dominate your career.",
  },
];

// Function to get icon based on title with dynamic size
const getIconForTitle = (title: string, isMobile = false) => {
  const iconSize = isMobile ? 18 : 24;

  switch (title) {
    case "Personalised Learning":
      return <FaUserGraduate size={iconSize} className="text-white" />;
    case "Skill-Based Curriculum":
      return <FaTools size={iconSize} className="text-white" />;
    case "AI-Powered Insights":
      return <FaRobot size={iconSize} className="text-white" />;
    case "Expert-Led Sessions":
      return <FaChalkboardTeacher size={iconSize} className="text-white" />;
    case "Interactive & Gamified Learning":
      return <FaGamepad size={iconSize} className="text-white" />;
    case "Learn Anytime, Anywhere":
      return <FaMobileAlt size={iconSize} className="text-white" />;
    case "Career & Certification Support":
      return <FaCertificate size={iconSize} className="text-white" />;
    default:
      return null;
  }
};

export default function Feature() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [maxScroll, setMaxScroll] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  const handleScroll = (direction: string) => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const cardWidth = container.firstChild
      ? (container.firstChild as HTMLElement).offsetWidth + 32
      : 300; // card width + gap
    const scrollAmount = direction === "left" ? -cardWidth : cardWidth;

    container.scrollBy({ left: scrollAmount, behavior: "smooth" });

    // Update scroll position after scrolling
    setTimeout(() => {
      setScrollPosition(container.scrollLeft);
      setMaxScroll(container.scrollWidth - container.clientWidth);
    }, 300);
  };

  // Initialize scroll values when component mounts
  const initScrollValues = () => {
    const container = scrollContainerRef.current;
    if (container) {
      setMaxScroll(container.scrollWidth - container.clientWidth);
    }
  };

  // Handle scroll event
  const handleScrollEvent = () => {
    const container = scrollContainerRef.current;
    if (container) {
      setScrollPosition(container.scrollLeft);
    }
  };

  // Handle responsive layout detection
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    // Set initial values
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="flex flex-col items-center gap-6 sm:gap-8 md:gap-10 w-full">
      <Header
        boldTitle={"What we offer"}
        title={"our students?"}
        subtitle="Everything you need to level up and achieve it."
      />

      <div className="relative w-full max-w-7xl px-3 sm:px-4">
        {/* Left scroll button */}
        <motion.button
          onClick={() => handleScroll("left")}
          disabled={scrollPosition <= 0}
          className={`absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full shadow-md p-1.5 sm:p-2 ${
            scrollPosition <= 0
              ? "opacity-50 cursor-not-allowed"
              : "hover:bg-gray-100"
          }`}
          aria-label="Scroll left"
          whileHover={scrollPosition > 0 ? { scale: 1.05, backgroundColor: "#f3f4f6" } : {}}
          whileTap={scrollPosition > 0 ? { scale: 0.95 } : {}}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <FaChevronLeft size={isMobile ? 18 : 24} />
        </motion.button>

        {/* Scrollable container */}
        <motion.div
          ref={scrollContainerRef}
          className="flex overflow-x-auto gap-4 sm:gap-6 md:gap-8 py-3 sm:py-4 scrollbar-hide"
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
          onScroll={handleScrollEvent}
          onLoad={initScrollValues}
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {tempData.map((item, index) => (
            <motion.div
              key={index}
              className="flex-shrink-0 w-64 sm:w-68 md:w-72"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <InfoCard
                title={item?.title}
                description={item?.description}
                icon={getIconForTitle(item?.title || "", isMobile)}
                delay={index}
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Right scroll button */}
        <motion.button
          onClick={() => handleScroll("right")}
          disabled={scrollPosition >= maxScroll}
          className={`absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full shadow-md p-1.5 sm:p-2 ${
            scrollPosition >= maxScroll
              ? "opacity-50 cursor-not-allowed"
              : "hover:bg-gray-100"
          }`}
          aria-label="Scroll right"
          whileHover={scrollPosition < maxScroll ? { scale: 1.05, backgroundColor: "#f3f4f6" } : {}}
          whileTap={scrollPosition < maxScroll ? { scale: 0.95 } : {}}
          initial={{ opacity: 0, x: 10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <FaChevronRight size={isMobile ? 18 : 24} />
        </motion.button>
      </div>
    </div>
  );
}
