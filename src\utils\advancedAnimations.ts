// Advanced animation variants for Framer Motion
import { Variants } from "framer-motion";

// Spring animations with physics
export const springUp: Variants = {
  hidden: { y: 100, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 15,
      mass: 1
    }
  }
};

export const springScale: Variants = {
  hidden: { scale: 0.8, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 10,
      mass: 0.8
    }
  }
};

// Staggered children with custom easing
export const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
      when: "beforeChildren"
    }
  }
};

// 3D rotation effects
export const rotate3d: Variants = {
  hidden: { 
    opacity: 0,
    rotateX: -20,
    rotateY: -20
  },
  visible: {
    opacity: 1,
    rotateX: 0,
    rotateY: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 10
    }
  }
};

// Magnetic hover effect (to be used with useMotionValue and useTransform)
export const magneticHover = {
  rest: { scale: 1 },
  hover: { scale: 1.1 }
};

// Advanced page transitions
export const pageTransitions: Variants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.98
  },
  enter: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: [0.645, 0.045, 0.355, 1.0], // Cubic bezier for smooth animation
      staggerChildren: 0.1
    }
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.3,
      ease: [0.645, 0.045, 0.355, 1.0]
    }
  }
};

// Parallax scroll effect
export const parallaxScroll = (strength: number = 100) => ({
  initial: { y: 0 },
  animate: (scrollY: number) => ({
    y: scrollY / strength,
    transition: { type: "tween", ease: "linear" }
  })
});

// Text reveal animation
export const textReveal: Variants = {
  hidden: { opacity: 0 },
  visible: (i: number) => ({
    opacity: 1,
    transition: {
      delay: i * 0.1
    }
  })
};

// Morphing shapes
export const morphShape: Variants = {
  circle: {
    borderRadius: "50%",
    transition: { duration: 0.5 }
  },
  square: {
    borderRadius: "0%",
    transition: { duration: 0.5 }
  }
};

// Bounce effect
export const bounce: Variants = {
  hidden: { y: 0 },
  visible: {
    y: [0, -20, 0],
    transition: {
      repeat: Infinity,
      repeatType: "reverse",
      duration: 1.5,
      ease: "easeInOut"
    }
  }
};

// Pulse effect
export const pulse: Variants = {
  hidden: { scale: 1 },
  visible: {
    scale: [1, 1.05, 1],
    transition: {
      repeat: Infinity,
      repeatType: "reverse",
      duration: 1.5,
      ease: "easeInOut"
    }
  }
};

// Wave effect (for text or elements in a row)
export const wave: Variants = {
  hidden: { y: 0 },
  visible: (i: number) => ({
    y: [0, -10, 0],
    transition: {
      delay: i * 0.05,
      repeat: Infinity,
      repeatType: "reverse",
      duration: 1,
      ease: "easeInOut"
    }
  })
};

// Flip card effect
export const flipCard: Variants = {
  front: {
    rotateY: 0,
    transition: { duration: 0.5 }
  },
  back: {
    rotateY: 180,
    transition: { duration: 0.5 }
  }
};

// Hover button effect with glow
export const glowButton = {
  rest: { 
    boxShadow: "0px 0px 0px rgba(66, 220, 163, 0)" 
  },
  hover: { 
    boxShadow: "0px 0px 20px rgba(66, 220, 163, 0.7)",
    transition: { duration: 0.3 }
  }
};

// Drag constraints with bounce back
export const dragWithBounce = {
  drag: {
    type: "spring",
    damping: 10,
    stiffness: 100,
    restDelta: 0.001
  }
};

// Scroll-triggered reveal animations
export const scrollRevealLeft: Variants = {
  hidden: { 
    opacity: 0, 
    x: -100 
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15
    }
  }
};

export const scrollRevealRight: Variants = {
  hidden: { 
    opacity: 0, 
    x: 100 
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15
    }
  }
};

export const scrollRevealUp: Variants = {
  hidden: { 
    opacity: 0, 
    y: 100 
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15
    }
  }
};

// Staggered list items
export const staggeredList: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

export const staggeredItem: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 200,
      damping: 20
    }
  }
};
