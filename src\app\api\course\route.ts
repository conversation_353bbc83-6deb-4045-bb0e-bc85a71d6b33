import { NextResponse } from "next/server";
import { Course } from "../../../backend/models"; // Using the barrel file
import { connectDB } from "../../../backend/lib/mongodb";

// Get all courses
export async function GET() {
  try {
    await connectDB();

    // Get all courses
    const result = await Course.find({}).exec();
    return NextResponse.json(result);
  } catch (error: unknown) {
    console.log(error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: errorMessage.includes("timeout") ? 504 : 500 }
    );
  }
}
