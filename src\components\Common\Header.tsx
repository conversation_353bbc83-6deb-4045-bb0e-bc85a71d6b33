"use client";

import { motion } from "framer-motion";

type HeaderProps = {
  boldTitle?: string;
  title?: string;
  subtitle?: string;
  boldTitleClassname?: string;
  titleClassname?: string;
  subtitleClassname?: string;
  isAboutUs?: boolean;
};

export default function Header({
  boldTitle,
  title,
  subtitle,
  boldTitleClassname,
  titleClassname,
  subtitleClassname,
  isAboutUs = false,
}: HeaderProps) {
  return (
    <motion.div
      className="flex flex-col items-center justify-center text-center gap-1 sm:gap-2 md:gap-3 p-2 sm:p-3 md:p-4"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.03 }}
    >
      <motion.span
        className={`${boldTitleClassname} p-1 text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-green-600 to-yellow-300 text-transparent bg-clip-text flex flex-wrap gap-1 sm:gap-2 justify-center`}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <motion.span
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          {boldTitle}
        </motion.span>{" "}
        <motion.span
          initial={{ opacity: 0, rotate: -20 }}
          animate={{ opacity: 1, rotate: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          whileHover={{ rotate: 20, scale: 1.2 }}
        >
          ✨
        </motion.span>
        <motion.span
          className={`${titleClassname} font-normal text-[#1C1C1C]`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          {title}
        </motion.span>
      </motion.span>
      {isAboutUs ? (
        <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 max-w-4xl mx-auto text-gray-500 hover:text-gray-700">
          <p className="mt-2">
            <b>GROW GRID</b> is proud to be Odisha’s first organised and trusted
            Ed-Tech platform 📘, created to help students not just in Odisha but
            across all of India. What began as a small idea is now a growing
            movement 🌱, changing how students learn, grow, and build their
            futures.
          </p>
          <p className="mt-2">
            We help bridge the gap between where you are and where you want to
            be by offering transparent, future-ready education 🎯. Whether
            you're a graduate or still pursuing your degree, our courses are
            designed to give you real-world skills that truly make a difference.
          </p>
          <p className="mt-2">
            At <b>GROW GRID</b>, we’re more than educators — we're your partners
            in success 🤝.
          </p>
          <p className="mt-2">
            Supporting Your Journey, Empowering Your Dreams 🚀
          </p>
        </div>
      ) : (
        <motion.p
          className={`${subtitleClassname} text-[10px] xs:text-xs sm:text-sm font-normal text-gray-500 hover:text-gray-700 transition-colors duration-300 px-2 sm:px-3 md:px-4 max-w-xs sm:max-w-sm md:max-w-md mx-auto`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          {subtitle}
          {subtitle && (
            <motion.span
              initial={{ x: -10 }}
              animate={{ x: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
              whileHover={{ x: 10 }}
            >
              🚀
            </motion.span>
          )}
        </motion.p>
      )}
    </motion.div>
  );
}
