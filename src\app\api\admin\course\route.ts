import { NextRequest, NextResponse } from "next/server";
import { Course } from "../../../../backend/models";
import { connectDB } from "../../../../backend/lib/mongodb";

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();

    // Validate request body
    if (!body.title || !body.slug) {
      return NextResponse.json(
        { success: false, message: "Please provide title and slug" },
        { status: 400 }
      );
    }

    const alreadyExists = await Course.findOne({ slug: body.slug });
    if (alreadyExists) {
      return NextResponse.json(
        { success: false, message: "Course already exists" },
        { status: 400 }
      );
    }

    const newCourse = await Course.create(body);
    return NextResponse.json(
      { success: true, data: newCourse },
      { status: 201 }
    );
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 400 }
    );
  }
}
