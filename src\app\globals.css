@import url("https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes funky-bounce {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(5deg);
  }
}

.funky-element {
  animation: funky-bounce 2s infinite;
}

.gradient-text {
  @apply bg-gradient-to-r from-purple-600 to-pink-600 text-transparent bg-clip-text;
}

.funky-card {
  @apply hover:bg-gradient-to-r from-purple-50 to-pink-50 transition-all duration-300 rounded-3xl hover:shadow-xl;
}

/* :root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

html {
  scroll-behavior: smooth;
  scroll-padding-top: 120px;
}

body {
  /* color: var(--foreground);
  background: var(--background); */
  font-family: "Lexend", sans-serif;
}

/* Grid pattern for 404 page */
.bg-grid-pattern {
  background-image:
    linear-gradient(to right, rgba(22, 163, 74, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(22, 163, 74, 0.1) 1px, transparent 1px);
  background-size: 30px 30px;
}
