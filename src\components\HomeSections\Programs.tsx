"use client";

import { useEffect, useState, useRef } from "react";
import Header from "../Common/Header";
import { motion } from "framer-motion";
import { getCourseAll } from "@/service/course";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Navigation, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import Link from "next/link";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import type { SwiperRef } from "swiper/react";

interface Course {
  _id: string;
  title: string;
  description: string;
  slug: string;
}

export default function Programs() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const swiperRef = useRef<SwiperRef>(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        const data = await getCourseAll();
        setCourses(data);
      } catch (error) {
        console.error("Error fetching courses:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  const handlePrev = () => {
    if (swiperRef.current && swiperRef.current.swiper && !isBeginning) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleNext = () => {
    if (swiperRef.current && swiperRef.current.swiper && !isEnd) {
      swiperRef.current.swiper.slideNext();
    }
  };

  const handleSlideChange = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      setIsBeginning(swiperRef.current.swiper.isBeginning);
      setIsEnd(swiperRef.current.swiper.isEnd);
    }
  };

  return (
    <div className="flex flex-col items-center gap-6 sm:gap-8 md:gap-10 w-full py-8 sm:py-12 md:py-16 bg-gradient-to-br from-green-50 to-green-100">
      <Header boldTitle={"Our"} title={"Programs"} />
      
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
        </div>
      ) : (
        <div className="w-full max-w-7xl px-4 sm:px-6 relative">
          {/* Custom navigation buttons */}
          <motion.button 
            className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full shadow-md p-3 sm:p-4 border border-green-100 hidden sm:flex items-center justify-center ${
              isBeginning ? 'opacity-50 cursor-not-allowed' : 'hover:bg-green-50'
            }`}
            onClick={handlePrev}
            // whileHover={!isBeginning ? { scale: 1.1, backgroundColor: "#f0fdf4" } : {}}
            // whileTap={!isBeginning ? { scale: 0.95 } : {}}
            style={{ marginLeft: "-30px" }}
            disabled={isBeginning}
          >
            <FaChevronLeft className={`${isBeginning ? 'text-gray-400' : 'text-green-600'}`} size={18} />
          </motion.button>
          
          <motion.button 
            className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full shadow-md p-3 sm:p-4 border border-green-100 hidden sm:flex items-center justify-center ${
              isEnd ? 'opacity-50 cursor-not-allowed' : 'hover:bg-green-50'
            }`}
            onClick={handleNext}
            // whileHover={!isEnd ? { scale: 1.1, backgroundColor: "#f0fdf4" } : {}}
            // whileTap={!isEnd ? { scale: 0.95 } : {}}
            style={{ marginRight: "-30px" }}
            disabled={isEnd}
          >
            <FaChevronRight className={`${isEnd ? 'text-gray-400' : 'text-green-600'}`} size={18} />
          </motion.button>
          
          <Swiper
            ref={swiperRef}
            modules={[Pagination, Navigation, Autoplay]}
            spaceBetween={30}
            slidesPerView={1}
            pagination={{ 
              clickable: true,
              bulletActiveClass: "swiper-pagination-bullet-active bg-green-500",
              bulletClass: "swiper-pagination-bullet bg-gray-300 opacity-70 w-3 h-3 my-2 mx-1",
            }}
            navigation={false} // Disable default navigation
            autoplay={{ delay: 5000, disableOnInteraction: false }}
            breakpoints={{
              640: {
                slidesPerView: 2,
                spaceBetween: 30,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 40,
              },
            }}
            className="py-12 px-2 sm:px-4 md:px-6"
            onSlideChange={handleSlideChange}
            onInit={handleSlideChange}
          >
            {courses.map((course) => (
              <SwiperSlide key={course._id} className="py-4 px-2">
                <Link href={`/courses/${course.slug}`}>
                  <motion.div 
                    className="bg-white rounded-xl overflow-hidden shadow-lg h-full flex flex-col border border-gray-100"
                    whileHover={{ 
                      y: -10,
                      boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                      borderColor: "#16a34a"
                    }}
                    transition={{ type: "spring", stiffness: 300, damping: 15 }}
                  >
                    <div className="relative h-48 w-full">
                      <img
                        src={`/assets/images/courses/${course.slug}.jpg`}
                        alt={course.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback image if the course image doesn't exist
                          (e.target as HTMLImageElement).src = "/assets/images/coursesBannerImage6.jpg";
                        }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                    </div>
                    <div className="p-6 flex-grow flex flex-col">
                      <h3 className="text-lg font-bold text-gray-800 mb-3">{course.title}</h3>
                      <p className="text-gray-600 text-sm line-clamp-3 mb-5 flex-grow">
                        {course.description || "Explore this comprehensive program designed to build your skills from fundamentals to advanced techniques."}
                      </p>
                      <div className="mt-auto">
                        <motion.span 
                          className="inline-block bg-green-100 text-green-800 text-xs px-4 py-1.5 rounded-full font-medium"
                          whileHover={{ 
                            backgroundColor: "#16a34a", 
                            color: "white",
                            scale: 1.05
                          }}
                          transition={{ duration: 0.2 }}
                        >
                          Learn More
                        </motion.span>
                      </div>
                    </div>
                  </motion.div>
                </Link>
              </SwiperSlide>
            ))}
          </Swiper>
          
          {/* Custom pagination styles */}
          <style jsx global>{`
            .swiper-pagination-bullet {
              width: 10px;
              height: 10px;
              background: #d1d5db;
              opacity: 0.7;
              margin: 0 5px;
            }
            .swiper-pagination-bullet-active {
              background: #16a34a;
              opacity: 1;
            }
            .swiper-container {
              padding: 30px 0;
            }
            .swiper-pagination {
              margin-top: 20px;
              position: relative;
              bottom: 0 !important;
            }
          `}</style>
        </div>
      )}
    </div>
  );
}
