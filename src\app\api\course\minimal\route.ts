import { NextResponse } from "next/server";
import Course from "@/backend/models/Courses"; // Using the barrel file
import { connectDB } from "@/backend/lib/mongodb";

// Get courses with only id, slug, and title
export async function GET(request: Request) {
  try {
    await connectDB();

    console.log("Fetching minimal course data");

    // Get courses with selected fields only
    const result = await Course.find({})
      .select('_id slug title')
      .exec();

    return NextResponse.json(result);
  } catch (error: unknown) {
    console.log(error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: errorMessage.includes("timeout") ? 504 : 500 }
    );
  }
}
