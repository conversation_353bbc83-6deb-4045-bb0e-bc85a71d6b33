import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";
import ScrollReveal from "@/components/Common/ScrollReveal";
import { courseFAQ } from "@/utils/helper";

interface Course {
  title: string;
  faq: Array<{ question: string; answer: string }>;
}

interface FAQContentProps {
  course?: Course;
}

const staggerContainer = {
  hidden: {},
  visible: { transition: { staggerChildren: 0.1 } }
};

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const FAQContent = ({ course }: FAQContentProps) => {
  const [activeFaqIndex, setActiveFaqIndex] = useState<number | null>(null);

  const toggleFAQ = (idx: number) => {
    setActiveFaqIndex(activeFaqIndex === idx ? null : idx);
  };
  return (
    <motion.section
      className="max-w-6xl mx-auto px-6 py-16"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true, amount: 0.1 }}
    >
      <ScrollReveal>
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-700 to-black">
            Frequently Asked Questions
          </h2>
          <div className="h-1 w-24 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Get answers to common questions about our {course?.title} program
          </p>
        </div>
      </ScrollReveal>

      <motion.div
        className="max-w-4xl mx-auto space-y-4"
        variants={staggerContainer}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
      >
        {/* {course?.faq?.map((faqItem: any, idx: number) => ( */}
         {courseFAQ.map((faqItem: any, idx: number) => (
          <motion.div
            key={idx}
            className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200"
            variants={fadeInUp}
            custom={idx}
            whileHover={{
              boxShadow:
                "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
            }}
          >
            <motion.button
              className="w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none"
              onClick={() => toggleFAQ(idx)}
              whileTap={{ scale: 0.99 }}
            >
              <h3 className="text-lg font-semibold text-gray-800">
                {faqItem.question}
              </h3>
              <motion.div
                animate={{ rotate: activeFaqIndex === idx ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-green-700"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </motion.div>
            </motion.button>

            <AnimatePresence>
              {activeFaqIndex === idx && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="overflow-hidden"
                >
                  <div className="px-6 pb-4 text-gray-600">
                    {faqItem.answer}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </motion.div>
    </motion.section>
  );
};
export default FAQContent;
