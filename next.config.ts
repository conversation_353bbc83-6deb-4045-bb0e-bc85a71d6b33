import type { NextConfig } from "next";

// Load environment variables explicitly
require('./next-env-load');

const nextConfig: NextConfig = {
  output: 'standalone', // optional, helpful for SSR
  reactStrictMode: true,
  images: {
    unoptimized: true, // helps avoid image-related 503s on Amplify
  },
  // Remove console logs in production
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'],
    } : false,
  },
  // Explicitly configure environment variables
  env: {
    // NODE_ENV is managed by Next.js and cannot be set here
    APP_ENV: process.env.APP_ENV || process.env.NODE_ENV || 'development',
    DB_PRODUCTION: process.env.DB_PRODUCTION,
    DB_TEST: process.env.DB_TEST,
    DB_DEVELOPMENT: process.env.DB_DEVELOPMENT,
    DB_LOCAL: process.env.DB_LOCAL,
    JWT_SECRET: process.env.JWT_SECRET,
  },
};

export default nextConfig;
