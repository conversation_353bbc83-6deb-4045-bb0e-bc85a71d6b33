"use client";
import AnimatedButton from "@/components/Common/AnimatedButton";
import ComingSoonSection from "@/components/CourseScreen/ComingSoonSection";
import CurriculumContent from "@/components/CourseScreen/CurriculumContent";
import FAQContent from "@/components/CourseScreen/FAQContent";
import LoaderComponent from "@/components/CourseScreen/LoaderComponent";
import RenderOverviewContent from "@/components/CourseScreen/RenderOverviewContent";
import TestimonialsContent from "@/components/CourseScreen/TestimonialsContent";
import ToolsContent from "@/components/CourseScreen/ToolsContent";
import Tracks from "@/components/CourseScreen/Tracks";
import RazorpayCheckout from "@/components/Payment/RazorpayCheckout";
import { getCourseBySlug } from "@/service/course";
import { usePlanStore } from "@/store/planStore";
import {
  fadeInUp,
  modalBackdrop,
  staggerContainer
} from "@/utils/animations";
import { redirectURL } from "@/utils/helper";
import {
  AnimatePresence,
  motion,
  useScroll,
  useTransform,
} from "framer-motion";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import {
  FaBars,
  FaClock,
  FaGraduationCap,
  FaHandshake,
  FaLaptop,
  FaTimes
} from "react-icons/fa";

interface CourseScreenProps {
  slug: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  terms?: string;
  background?: string;
}

const CourseScreen = ({ slug }: CourseScreenProps) => {
  const [course, setCourse] = useState<any>(null);
  const [activePhase, setActivePhase] = useState(0);
  const [activeTab, setActiveTab] = useState("overview");
  const [enrollmentOpen, setEnrollmentOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeFaqIndex, setActiveFaqIndex] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    background: "",
    termsAccepted: false,
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [paymentDetails, setPaymentDetails] = useState({
    amount: 0,
    courseId: "",
    courseName: "",
    planId: "",
    name: "",
    email: "",
    phone: "",
  });
  // const { planSelected } = usePlanStore();

  // const router = useRouter();

  const getCourseData = async () => {
    setIsLoading(true);
    try {
      const { data } = await getCourseBySlug(slug);
      setCourse(data);
    } catch (error) {
      console.error("Error fetching course data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getCourseData();
  }, [slug]);

  const handleInputChange = (e: any) => {
    const { id, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: type === "checkbox" ? checked : value,
    }));
  };

  const toggleFAQ = (index: number) => {
    setActiveFaqIndex(activeFaqIndex === index ? null : index);
  };

  // Create a ref for the Tracks section
  const tracksRef = useRef<HTMLDivElement>(null);

  // Function to scroll to Tracks section
  const scrollToTracks = () => {
    setActiveTab("track");
    setTimeout(() => {
      tracksRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  // Update the Apply Now button click handler
  const handleApplyNowClick = () => {
    // scrollToTracks();
    try {
      window.open(redirectURL?.applyNow, "_blank");
    } catch (error) {
      console.error("Error opening apply now form:", error);
    }
  };

  // Determine the order of content based on active tab
  const renderContent = () => {
    type TabKey = "overview" | "curriculum" | "tools" | "testimonials" | "faq" | "track";
    type ContentMap = {
      [K in TabKey]: {
        component: React.ComponentType<any>;
        props: Record<string, any>;
      };
    };

    // Define content components map
    const contentMap: ContentMap = {
      overview: { component: RenderOverviewContent, props: { course } },
      curriculum: {
        component: CurriculumContent,
        props: { activePhase, setActivePhase, course },
      },
      tools: { component: ToolsContent, props: { course } },
      testimonials: { component: TestimonialsContent, props: {} },
      faq: { component: FAQContent, props: { course } },
      track: {
        component: Tracks,
        props: { setEnrollmentOpen, ref: tracksRef },
      },
    };

    // Define content order
    const contentOrder: TabKey[] = [
      "track",
      "overview",
      "curriculum",
      "tools",
      "testimonials",
      "faq",
    ];

    // Render active content
    const ActiveComponent =
      contentMap[activeTab as TabKey]?.component ||
      contentMap.overview.component;
    const activeContent = (
      <ActiveComponent
        key={`active-${activeTab}`}
        {...contentMap[activeTab as TabKey]?.props}
      />
    );

    // Render remaining content
    const remainingContent = contentOrder
      .filter((tab) => tab !== activeTab)
      .map((tab) => {
        const { component: Component, props } = contentMap[tab];
        return <Component key={tab} {...props} />;
      });

    return (
      <>
        {activeContent}
        {remainingContent}
      </>
    );
  };

  // Reference for scroll animations
  const scrollRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: scrollRef,
    offset: ["start start", "end start"],
  });

  // Transform values for parallax effect
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const textY = useTransform(scrollYProgress, [0, 1], ["0%", "100%"]);
  

  // Render loader if loading, Coming Soon if course is null, otherwise render the course content
  if (isLoading) {
    return <LoaderComponent />;
  } else if (!course) {
    return <ComingSoonSection />;
  }

  const validateForm = () => {
    const errors: FormErrors = {};
    if (!formData.name.trim()) errors.name = "Name is required";
    if (!formData.email.trim()) errors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.email))
      errors.email = "Email is invalid";
    if (!formData.phone.trim()) errors.phone = "Phone is required";
    if (!formData.termsAccepted) errors.terms = "You must accept the terms";

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleApplyNow = async (e: any) => {
    e.preventDefault();

    // if (!validateForm()) return;

    // try {
    //   setIsSubmitting(true);
    //   const response = await createRequest({
    //     name: formData.name,
    //     email: formData.email,
    //     phone: formData.phone,
    //     details: formData.background,
    //     course: course?.title || slug,
    //     courseId: course?._id || "",
    //     type: "apply-course",
    //   });

    //   // Success handling
    //   setEnrollmentOpen(false);
    //   setFormData({
    //     name: "",
    //     email: "",
    //     phone: "",
    //     background: "",
    //     termsAccepted: false,
    //   });

    //   console.log(planSelected ?.planType, "planSelected");
    //   // Determine plan ID based on course type
    //   if (!planSelected?.planType) {
    //     throw new Error("No plan selected");
    //   }

    //   // Redirect to payment or show payment component
    //   setShowPayment(true);
    //   setPaymentDetails({
    //     amount: planSelected?.amount,
    //     courseId: course?._id || "",
    //     courseName: course?.title || "",
    //     planId: planSelected?.planType,
    //     name: formData.name,
    //     email: formData.email,
    //     phone: formData.phone,
    //   });
    //   setIsSubmitting(false);
    // } catch (error:any) {
    //   setIsSubmitting(false);

    //    if (error.response?.data?.error) {
    //     const apiErrors = error.response.data.error;
    //     const newFormErrors: any = { ...formErrors };

    //     apiErrors.forEach((err: any) => {
    //       if (err.path && err.path[0]) {
    //         const fieldName = err.path[0];
    //         newFormErrors[fieldName] = err.message;
    //       }
    //     });

    //     setFormErrors(newFormErrors);
    //     toast.error("Please fix the form errors");
    //   } else {
    //     toast.error(error.response?.data?.message || "Failed to submit application");
    //   }
    // }
    console.log(
      "Apply Now clicked, but form submission is disabled for demo purposes"
    );
  };

  return (
    <motion.div
      className="bg-slate-50 min-h-screen"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Hero Section */}
      <motion.header
        ref={scrollRef}
        className="bg-gradient-to-r from-black to-green-800 text-white py-16 sm:py-20 md:py-24 text-center relative overflow-hidden"
        style={{ minHeight: "min(90vh, 400px)" }}
      >
        <motion.div
          className="absolute inset-0 bg-[url('/api/placeholder/1200/400')] bg-cover bg-center"
          style={{
            y: backgroundY,
            opacity: 0.2,
          }}
        ></motion.div>

        <motion.div
          className="relative z-10 max-w-5xl mx-auto px-4 sm:px-6 h-full flex flex-col justify-center py-8"
          style={{ y: textY }}
        >
          {/* <motion.h1
            className="text-3xl sm:text-4xl md:text-6xl font-extrabold mb-3 sm:mb-4 leading-tight"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-white">
              {course?.title}
            </span>
          </motion.h1> */}
          <motion.div
            // className="h-1 w-24 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-4"
            // initial={{ width: 0 }}
            // animate={{ width: "6rem" }}
            className="text-3xl sm:text-4xl md:text-6xl font-extrabold mb-3 sm:mb-4 leading-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-white">
              {course?.title || "Course Title"}
            </span>
          </motion.div>

          <motion.div
            className="h-1 w-16 sm:w-24 bg-green-500 mx-auto my-4 sm:my-6"
            initial={{ width: 0 }}
            animate={{ width: "6rem" }}
            transition={{ duration: 0.8, delay: 0.5 }}
          ></motion.div>

          <motion.p
            className="text-base sm:text-lg md:text-xl max-w-2xl mx-auto mb-8 sm:mb-10 px-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.7, delay: 0.7 }}
          >
            {course?.outcomes}
          </motion.p>

          <motion.div
            className="flex flex-wrap justify-center gap-3 sm:gap-4 mt-6 sm:mt-8"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            <motion.div
              className="bg-black bg-opacity-40 backdrop-blur-sm px-4 sm:px-6 py-3 sm:py-4 rounded-lg flex items-center border border-green-500/30 text-sm sm:text-base"
              variants={fadeInUp}
              whileHover={{
                scale: 1.05,
                boxShadow: "0 0 20px rgba(66, 220, 163, 0.3)",
              }}
            >
              <FaClock className="text-xl sm:text-2xl mr-2 sm:mr-3 text-green-400" />
              <span className="font-semibold">{course?.duration}</span>
            </motion.div>

            <motion.div
              className="bg-black bg-opacity-40 backdrop-blur-sm px-4 sm:px-6 py-3 sm:py-4 rounded-lg flex items-center border border-green-500/30 text-sm sm:text-base"
              variants={fadeInUp}
              transition={{ delay: 0.1 }}
              whileHover={{
                scale: 1.05,
                boxShadow: "0 0 20px rgba(66, 220, 163, 0.3)",
              }}
            >
              <FaLaptop className="text-xl sm:text-2xl mr-2 sm:mr-3 text-green-400" />
              <span className="font-semibold">
                {course?.total_projects} Real Projects
              </span>
            </motion.div>

            <motion.div
              className="bg-black bg-opacity-40 backdrop-blur-sm px-4 sm:px-6 py-3 sm:py-4 rounded-lg flex items-center border border-green-500/30 text-sm sm:text-base"
              variants={fadeInUp}
              transition={{ delay: 0.2 }}
              whileHover={{
                scale: 1.05,
                boxShadow: "0 0 20px rgba(66, 220, 163, 0.3)",
              }}
            >
              <FaHandshake className="text-xl sm:text-2xl mr-2 sm:mr-3 text-green-400" />
              <span className="font-semibold">Industry Partners</span>
            </motion.div>
          </motion.div>
        </motion.div>

        <Image
          src={
            course?.slug
              ? `/assets/images/courses/${course.slug}.jpg`
              : "/assets/images/coursesBannerImage6.jpg"
          }
          alt="background"
          layout="fill"
          objectFit="cover"
          className="backdrop-blur-sm absolute inset-0 opacity-60"
          priority
        />
      </motion.header>
      {/* Navigation */}
      <motion.nav
        className="bg-gradient-to-r from-black to-green-900 text-white px-6 py-4 sticky top-0 z-40 shadow-lg"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ type: "spring", stiffness: 100, damping: 20 }}
      >
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <motion.div
            className="flex items-center"
            whileHover={{ scale: 1.05 }}
          >
            <motion.div
              className="bg-green-500 text-black p-2 rounded-full mr-3"
              whileHover={{ rotate: 10 }}
              whileTap={{ scale: 0.9 }}
            >
              <FaGraduationCap className="text-xl md:text-2xl" />
            </motion.div>
            <h2 className="font-bold text-xl md:text-2xl text-white truncate max-w-[200px] md:max-w-[300px]">
              {course?.title}
            </h2>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-8">
            {["overview", "curriculum", "tools", "testimonials", "faq"]
              .concat(
                course?.courseType === "Internship"
                  ? ["track"]
                  : course?.courseType === "domain"
                  ? ["track"]
                  : []
              )
              .map((tab) => (
                <motion.button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`relative px-2 py-1 ${
                    activeTab === tab
                      ? "text-green-400 font-semibold"
                      : "text-gray-300"
                  }`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {tab === "track" && course?.courseType === "Internship"
                    ? "Live Track"
                    : tab === "track" && course?.courseType === "domain"
                    ? "Self Track"
                    : tab === "faq"
                    ? "FAQ"
                    : tab.charAt(0).toUpperCase() + tab.slice(1)}
                  {activeTab === tab && (
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-green-400"
                      layoutId="activeTab"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{
                        type: "spring",
                        stiffness: 300,
                        damping: 30,
                      }}
                    />
                  )}
                </motion.button>
              ))}
          </div>

          {/* Mobile Navigation Toggle */}
          <div className="md:hidden">
            <motion.button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="text-white p-2"
              whileTap={{ scale: 0.9 }}
            >
              <FaBars className="text-xl" />
            </motion.button>
          </div>

          <AnimatedButton
            onClick={handleApplyNowClick}
            className="hidden md:block"
            variant="primary"
          >
            Apply Now
          </AnimatedButton>
        </div>

        {/* Mobile Navigation Menu */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div
              className="md:hidden absolute top-full left-0 right-0 bg-black bg-opacity-95 border-t border-green-800 z-40"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex flex-col p-4 space-y-3">
                {["overview", "curriculum", "tools", "testimonials", "faq"]
                  .concat(
                    course?.courseType === "Internship"
                      ? ["track"]
                      : course?.courseType === "domain"
                      ? ["track"]
                      : []
                  )
                  .map((tab) => (
                    <motion.button
                      key={tab}
                      onClick={() => {
                        setActiveTab(tab);
                        setMobileMenuOpen(false);
                      }}
                      className={`py-2 px-4 rounded-md ${
                        activeTab === tab
                          ? "bg-green-900 text-green-400 font-semibold"
                          : "text-gray-300"
                      }`}
                      whileTap={{ scale: 0.95 }}
                    >
                      {tab === "track" && course?.courseType === "Internship"
                        ? "Live Track"
                        : tab === "track" && course?.courseType === "domain"
                        ? "Self Track"
                        : tab === "faq"
                        ? "FAQ"
                        : tab.charAt(0).toUpperCase() + tab.slice(1)}
                    </motion.button>
                  ))}
                <AnimatedButton
                  onClick={() => {
                    handleApplyNowClick();
                    setMobileMenuOpen(false);
                  }}
                  variant="primary"
                  className="mt-4"
                >
                  Apply Now
                </AnimatedButton>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* Enrollment Form (Conditional) */}
      <AnimatePresence>
        {enrollmentOpen && (
          <>
            <motion.div
              className="fixed inset-0 bg-black z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.7 }}
              exit={{ opacity: 0 }}
              onClick={() => setEnrollmentOpen(false)}
              variants={modalBackdrop}
            />
            <motion.div
              className="fixed inset-0 flex items-center justify-center z-50 p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="bg-gradient-to-br from-black to-green-900 text-white p-8 rounded-xl shadow-2xl max-w-md w-full border border-green-500/30"
                initial={{ scale: 0.9, y: 20, opacity: 0 }}
                animate={{ scale: 1, y: 0, opacity: 1 }}
                exit={{ scale: 0.9, y: 20, opacity: 0 }}
                transition={{ type: "spring", damping: 25, stiffness: 300 }}
              >
                <div className="flex justify-between items-center mb-6">
                  <motion.h3
                    className="text-2xl font-bold text-green-400"
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.1 }}
                  >
                    Apply for Internship
                  </motion.h3>
                  <motion.button
                    onClick={() => setEnrollmentOpen(false)}
                    className="text-gray-400 hover:text-white rounded-full p-1"
                    whileHover={{ scale: 1.1, rotate: 90 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </motion.button>
                </div>

                <motion.form
                  className="space-y-5"
                  variants={staggerContainer}
                  initial="hidden"
                  animate="visible"
                  onSubmit={handleApplyNow}
                >
                  <motion.div variants={fadeInUp}>
                    <label
                      className="block text-green-300 text-sm font-medium mb-2"
                      htmlFor="name"
                    >
                      Full Name
                    </label>
                    <motion.input
                      className={`bg-black/50 border ${
                        formErrors.name
                          ? "border-red-500"
                          : "border-green-700/50"
                      } rounded-lg w-full py-3 px-4 text-white leading-tight focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent`}
                      id="name"
                      type="text"
                      placeholder="John Doe"
                      value={formData.name}
                      onChange={handleInputChange}
                      whileFocus={{ scale: 1.01 }}
                    />
                    {formErrors.name && (
                      <p className="text-red-500 text-xs mt-1">
                        {formErrors.name}
                      </p>
                    )}
                  </motion.div>

                  <motion.div variants={fadeInUp}>
                    <label
                      className="block text-green-300 text-sm font-medium mb-2"
                      htmlFor="email"
                    >
                      Email
                    </label>
                    <motion.input
                      className={`bg-black/50 border ${
                        formErrors.email
                          ? "border-red-500"
                          : "border-green-700/50"
                      } rounded-lg w-full py-3 px-4 text-white leading-tight focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent`}
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={handleInputChange}
                      whileFocus={{ scale: 1.01 }}
                    />
                    {formErrors.email && (
                      <p className="text-red-500 text-xs mt-1">
                        {formErrors.email}
                      </p>
                    )}
                  </motion.div>

                  <motion.div variants={fadeInUp}>
                    <label
                      className="block text-green-300 text-sm font-medium mb-2"
                      htmlFor="phone"
                    >
                      Phone
                    </label>
                    <motion.input
                      className={`bg-black/50 border ${
                        formErrors.phone
                          ? "border-red-500"
                          : "border-green-700/50"
                      } rounded-lg w-full py-3 px-4 text-white leading-tight focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent`}
                      id="phone"
                      type="tel"
                      placeholder="(*************"
                      value={formData.phone}
                      onChange={handleInputChange}
                      whileFocus={{ scale: 1.01 }}
                    />
                    {formErrors.phone && (
                      <p className="text-red-500 text-xs mt-1">
                        {formErrors.phone}
                      </p>
                    )}
                  </motion.div>

                  <motion.div variants={fadeInUp}>
                    <label
                      className="block text-green-300 text-sm font-medium mb-2"
                      htmlFor="background"
                    >
                      Background
                    </label>
                    <motion.textarea
                      className={`bg-black/50 border ${
                        formErrors.background
                          ? "border-red-500"
                          : "border-green-700/50"
                      } rounded-lg w-full py-3 px-4 text-white leading-tight focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent`}
                      id="background"
                      placeholder="Tell us about your background and why you're interested in this program"
                      value={formData.background}
                      onChange={handleInputChange}
                      whileFocus={{ scale: 1.01 }}
                    />
                    {formErrors.background && (
                      <p className="text-red-500 text-xs mt-1">
                        {formErrors.background}
                      </p>
                    )}
                  </motion.div>

                  <motion.div className="flex items-center" variants={fadeInUp}>
                    <motion.input
                      type="checkbox"
                      id="termsAccepted"
                      checked={formData.termsAccepted}
                      onChange={handleInputChange}
                      className={`mr-2 h-4 w-4 accent-green-500 ${
                        formErrors.terms ? "ring-2 ring-red-500" : ""
                      }`}
                      whileHover={{ scale: 1.2 }}
                    />
                    <label
                      className="text-sm text-gray-300"
                      htmlFor="termsAccepted"
                    >
                      I agree to the terms and conditions
                    </label>
                    {formErrors.terms && (
                      <p className="text-red-500 text-xs ml-2">
                        {formErrors.terms}
                      </p>
                    )}
                  </motion.div>

                  <motion.div variants={fadeInUp}>
                    <AnimatedButton
                      type="submit"
                      className="w-full py-3 text-lg"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Submitting..." : "Submit Application"}
                    </AnimatedButton>
                  </motion.div>
                </motion.form>
              </motion.div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {renderContent()}

      <motion.div
        className="bg-gradient-to-r from-black to-green-900 text-white shadow-lg p-8 sm:p-10 md:p-12 text-center relative overflow-hidden"
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7 }}
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/api/placeholder/1200/400')] bg-cover bg-center"></div>
        </div>

        <div className="relative z-10">
          <motion.h3
            className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-6 text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-white"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            viewport={{ once: true }}
          >
            Ready to Transform Your Career?
          </motion.h3>

          <motion.p
            className="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 max-w-2xl mx-auto px-2"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            viewport={{ once: true }}
          >
            Join our Digital Marketing Internship program and gain the skills,
            experience, and portfolio that employers are looking for.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{
              delay: 0.6,
              duration: 0.5,
              type: "spring",
              stiffness: 200,
            }}
            viewport={{ once: true }}
          >
            <AnimatedButton
              onClick={handleApplyNowClick}
              className="px-6 sm:px-10 py-3 sm:py-4 text-base sm:text-lg"
              variant="primary"
            >
              Apply Now
            </AnimatedButton>
          </motion.div>
        </div>
      </motion.div>

      {/* Payment Modal */}
      <AnimatePresence>
        {showPayment && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-green-600">
                  Complete Payment
                </h3>
                <button
                  onClick={() => setShowPayment(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <FaTimes />
                </button>
              </div>

              <p className="mb-6 text-gray-700">
                Complete your payment to enroll in {paymentDetails.courseName}
              </p>

              <RazorpayCheckout
                id={course?.id || ""}
                planId={paymentDetails.planId}
                amount={paymentDetails.amount}
                courseId={paymentDetails.courseId}
                courseName={paymentDetails.courseName}
                name={paymentDetails.name}
                email={paymentDetails.email}
                phone={paymentDetails.phone}
                onSuccess={(data) => {
                  setShowPayment(false);
                  // Handle successful payment
                  // You might want to show a success message or redirect
                }}
                onError={(error) => {
                  console.error("Payment error:", error);
                  // Handle payment error
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default CourseScreen;
