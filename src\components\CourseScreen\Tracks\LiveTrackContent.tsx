import TrackButton from "@/components/Buttons/TrackButton";
import { usePlanStore } from "@/store/planStore";
import { redirectURL } from "@/utils/helper";
import { plansIds } from "@/utils/payment";
import { FaCheckCircle } from "react-icons/fa";

interface LiveTrackContentProps {
  setEnrollmentOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const LiveTrackContent = ({ setEnrollmentOpen }: LiveTrackContentProps) => {
  const { setPlanSelected } = usePlanStore();
  // Function to handle enrollment button click
  const handleEnrollmentClick = () => {
    try {
      setPlanSelected(plansIds?.live);
      setEnrollmentOpen(false);
      window.open(redirectURL.lms, '_blank');
    } catch (error) {
      console.error("Error opening enrollment link:", error);
    }
  };

  return (
    <div className="p-6 sm:p-8 flex flex-col h-full">
      <div className="text-center mb-6">
        <h2 className="text-2xl sm:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-green-600">
          LIVE TRACK
        </h2>
        <div className="h-1 w-20 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-4"></div>
        <p className="text-sm sm:text-base text-gray-300 mx-auto">
          Guided Industrial Training, Powered by Experts
        </p>
      </div>

      {/* Price */}
      <div className="text-center mb-8">
        <div className="text-lg text-green-400 font-semibold mb-2">
          Program Fee
        </div>
        <div className="text-4xl sm:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-white mb-2">
          ₹15,000
        </div>
        <div className="h-0.5 w-16 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-3"></div>
        <div className="text-gray-300 text-sm">
          One-time payment for complete access to the Live Track program
        </div>
      </div>

      {/* Features list */}
      <div className="space-y-4 flex-grow mb-8">
        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Live Mentor Support, When It Matters Most
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Get real-time doubt resolution from experienced mentors—because
              the right guidance makes all the difference.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              1-on-1 Mentorship for Personalized Growth
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Work closely with dedicated mentors for hands-on, customized
              support tailored to your learning journey.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Interactive Doubt-Clearing Sessions
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Stay confident and clear with regular sessions designed to address
              your specific challenges.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Curriculum Designed Around You
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Enjoy a carefully crafted, learner-friendly curriculum that's
              streamlined for clarity, focus, and practical outcomes.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Unlimited Access, Anytime Learning
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Access all recorded sessions through our LMS for life—review,
              revisit, and master concepts at your own pace.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Stand Out with Exceptional Recognition
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Earn a prestigious certificate upon course completion, with
              special accolades for those who take on and excel in advanced
              projects.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Evaluate, Improve, Excel
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Ongoing assessments and skill tests help you gauge your
              understanding and continuously level up.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Career-Ready from Day One
            </p>
            <p className="text-gray-400 text-xs mt-1">
              📄 Resume Building – Craft a standout resume that speaks to your
              strengths
              <br />
              🧭 Job Market Prep – Get insights and tools to position yourself
              for top opportunities
              <br />
              🎯 Interview Preparation – Practice with mock interviews and
              detailed feedback
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Hands-On Industry Projects
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Gain real-world experience by working on 3+ application based
              projects that simulate real challenges and scenarios.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Earn While You Learn
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Unlock stipend opportunities based on key performance metrics like
              attendance, engagement, and mentor evaluations.
            </p>
          </div>
        </div>
      </div>

      {/* CTA Button */}

      <TrackButton
        comingSoon={true}
        price={15000}
        onclick={handleEnrollmentClick}
        name="Enroll for Live Track"
      />
    </div>
  );
};

export default LiveTrackContent;
