import { motion } from "framer-motion";
import { FaStar } from "react-icons/fa";
import ScrollReveal from "@/components/Common/ScrollReveal";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Navigation, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";

interface Testimonial {
  title: string;
  text: string;
  name: string;
  position: string;
  course: string;
  batch: string;
  university: string;
  universityLogo?: string;
}

const testimonials: Testimonial[] = [
  {
    title: "A Transformative Full Stack Web Development Journey",
    text: "My two-month internship with GrowGrid (https://growgrid.co.in/) was an extraordinary journey of growth, learning, and skill-building in End-to-End Full Stack Web Development. This immersive program opened the door to the dynamic world of front-end and back-end development, empowering me to refine my technical expertise and deepen my understanding of full-stack projects. Throughout the internship, I gained hands-on experience in crafting intricate designs, performing simulations, and mastering essential tools like HTML5, Node.js, and hosting platforms such as Vercel, Netlify, and Heroku. These practical insights have equipped me with a strong foundation to tackle future projects and advance my career in web development. I'm incredibly grateful for the unwavering support of GrowGrid's dedicated instructors and mentors, whose guidance made this experience truly transformative. This internship has not only honed my technical skills but also ignited a passion for creating innovative, impactful solutions. I highly recommend this program to any aspiring developer eager to take their skills to the next level!",
    name: "Abhisek A Rama",
    position: "Student",
    course: "END TO END FULL STACK WEB DEVELOPMENT",
    batch: "May 2025 Batch",
    university: "SRM UNIVERSITY",
    universityLogo: "https://upload.wikimedia.org/wikipedia/en/thumb/7/7a/SRM_Institute_of_Science_and_Technology_Logo.svg/1200px-SRM_Institute_of_Science_and_Technology_Logo.svg.png",
  },
  {
    title: "Transforming Data into Life-Saving Insights",
    text: "My Machine Learning internship with GrowGrid and Personify was an exhilarating and transformative experience! Working on a live project focused on predicting diabetes in patients, I had the opportunity to dive deep into algorithms like SVM and Naive Bayes, rigorously testing them to identify the most accurate models. This hands-on journey not only sharpened my technical expertise but also ignited a profound passion for leveraging AI to revolutionize healthcare. The program equipped me with practical skills that have boosted my confidence and preparedness for job interviews, inspiring me to pursue exciting opportunities in data analytics. The guidance from GrowGrid’s exceptional mentors was instrumental in making this experience unforgettable. I wholeheartedly recommend this program to anyone eager to harness the power of machine learning for meaningful impact!",
    name: "K Savita",
    position: "Student",
    course: "Ai Powered MACHINE LEARNING",
    batch: "May 2025 Batch",
    university: "Narsimha Reddy Engineering College",
    universityLogo: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSdJPvQIqSthL___1U-0_5u4W0-PtVYdYPM5g&s",
  },
  {
    title: "A Game-Changing Data Science Journey",
    text: "My Data Science internship with GrowGrid, in collaboration with tools like Google Colab, AWS, Kaggle, TensorFlow, and PyTorch, was an unforgettable and transformative experience. Working on a live project, I gained hands-on expertise with Python, Pandas, and machine learning algorithms, bringing concepts I had only studied into real-world application. From cleaning and preparing datasets to building models and presenting actionable insights, this program gave me a comprehensive understanding of the data science pipeline. The opportunity to apply these skills during an internship with Personifwy was a highlight, allowing me to tackle real-world challenges and solidify my expertise. This experience has not only prepared me for a thriving career in data science but also fueled my passion for turning data into meaningful solutions. A heartfelt thank you to GrowGrid’s exceptional mentors for their guidance and support! I highly recommend GrowGrid’s program to anyone seeking authentic, hands-on exposure to data science. It’s the perfect launchpad for aspiring data scientists!",
    name: "Praveen Reddy",
    position: "Student",
    course: "APPLIED DATA SCIENCE",
    batch: "May 2025 Batch",
    university: "Veltech University",
    universityLogo: "https://upload.wikimedia.org/wikipedia/en/e/e9/Veltech_Rangarajan_Dr._Sagunthala_R%26D_Institute_of_Science_and_Technology_logo.png",
  },
  {
    title: "An Empowering Cyber Security Adventure",
    text: "Enrolling in GrowGrid’s Cyber Security live projects during my summer vacation as a second-year college student was a game-changing decision! This immersive program plunged me into the world of real-world cyber threats and defensive strategies, offering hands-on experience that sharpened my critical thinking and problem-solving skills. Through challenging projects, I gained practical insights into cybersecurity that transformed my understanding of the field. Earning a certificate at the end of the program was a proud moment, and it now strengthens my resume as I pursue a career in this dynamic industry. This experience has significantly boosted my confidence and made me a more competitive candidate in the job market. I wholeheartedly recommend GrowGrid’s Cyber Security program to anyone passionate about safeguarding the digital world. It’s an unparalleled opportunity to gain real-world skills and kickstart your career!",
    name: "Kanha Pattnaik",
    position: "Student",
    course: "CYBER SECURITY & RISK MANAGEMENT",
    batch: "May 2025 Batch",
    university: "KIIT University",
    universityLogo: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRYDXHve3wBvopRRbF6Ded0o1L94QfgDtzd-w&s",
  },
  {
    title: "From Novice to Confident Web Developer",
    text: "When I joined GrowGrid’s End-to-End Full Stack Web Development program, I was unsure and overwhelmed about front-end development. However, the program’s clear, step-by-step approach transformed learning into an engaging and rewarding experience. By the end, I had built a website I’m incredibly proud of a testament to the skills I gained! The hands-on projects and supportive mentorship provided by GrowGrid were instrumental in building my confidence and laying a strong foundation in web development. This journey not only equipped me with practical skills but also opened my eyes to the vast opportunities in the tech industry. I’m thrilled about where these newfound abilities will take me and highly recommend GrowGrid’s program to anyone eager to kickstart their tech career!",
    name: "Subham Sahoo",
    position: "Student",
    course: "END TO END FULL STACK WEB DEVELOPMENT",
    batch: "May 2025 Batch",
    university: "SOA ITER",
    universityLogo: "https://images.squarespace-cdn.com/content/v1/57713a8e2994cae381dd86fe/1510405002757-FP2W9HV8ZA0HP2IF3O6A/SOA-PNG.png",
  },
  {
    title: "A Transformative Digital Marketing Experience",
    text: "My journey with GrowGrid’s Digital Marketing for Business Transformation program was nothing short of exceptional, and I’m immensely grateful for this incredible opportunity. Under the expert guidance of my mentor, [Mentor’s Name], I gained invaluable knowledge and practical skills that have reshaped my understanding of digital marketing. The hands-on approach and insightful mentorship provided a strong foundation, empowering me to confidently apply these skills in future endeavors. This experience has fueled my passion for driving business transformation through digital strategies. I wholeheartedly recommend GrowGrid’s program to anyone looking to excel in the dynamic world of digital marketing!",
    name: "Nabin Das",
    position: "Student",
    course: "Digital Marketing for Business Transformation",
    batch: "May 2025 Batch",
    university: "CUTM, BBSR",
    universityLogo: "https://upload.wikimedia.org/wikipedia/en/thumb/6/62/Centurion_University_of_Technology_and_Management_Logo.svg/1200px-Centurion_University_of_Technology_and_Management_Logo.svg.png",
  },
  {
    title: "A Transformative Digital Marketing Experience",
    text: "To be honest, I joined Grow Grid thinking it was just another online program. But it turned out to be way more than that. It felt like a real internship  from working on live projects to getting feedback like you’d get in an actual company environment. The mentors were super helpful and made sure we understood how the real tech world works. What stood out the most was how practical everything was  we weren’t just learning theory; we were actually building stuff. Looking back, I can confidently say that my investment was 100% worth it. This program gave me the skills, the confidence, and the exposure I needed. Huge thanks to the Grow Grid team for such a solid experience!",
    name: "Saurav Pattnaik",
    position: "Student",
    course: "Full Stack Web Development",
    batch: "May 2025 Batch",
    university: "SOA ITER",
    universityLogo: "https://images.squarespace-cdn.com/content/v1/57713a8e2994cae381dd86fe/1510405002757-FP2W9HV8ZA0HP2IF3O6A/SOA-PNG.png",
  },
  {
    title: "A Transformative Digital Marketing Experience",
    text: "When I joined the Digital Marketing program at Grow Grid, I expected a basic course with recorded videos and some assignments. But honestly, it turned out to be so much more. It wasn’t just a program  it felt like a proper internship experience. We worked on real campaigns, learned how businesses actually grow online, and got hands-on with tools that digital marketers use every day. The sessions were super engaging, and the mentors actually guided us like a team not just like students. What makes me even more proud is being part of the 1st edtech startup of Odisha. It feels amazing to say I was one of Grow Grid’s early consumers, and I genuinely learned something valuable here. If you're serious about learning digital marketing the right way  this is the place.",
    name: "Subhra Pattnaik",
    position: "Student",
    course: "Digital Marketing for Business Transformation",
    batch: "May 2025 Batch",
    university: "C.V. Raman Global University",
    universityLogo: "https://upload.wikimedia.org/wikipedia/en/6/6a/C._V._Raman_Global_University_Logo.png",
  },
  {
    title: "A Transformative Digital Marketing Experience",
    text: "I got the kind of structured classroom learning that my college wasn’t providing. The concepts were explained clearly, and everything was super practical  not just theory from slides. What helped me a lot was their customer support. Whenever I was stuck or had doubts, the team was quick to respond and actually cared about solving the issue. From my side, I’d easily give it a 5 out of 5. If you really want to understand how digital marketing works  not just the buzzwords  Grow Grid is the right place to start.",
    name: "Sahil Behera",
    position: "Student",
    course: "Digital Marketing for Business Transformation",
    batch: "May 2025 Batch",
    university: "SOA ITER",
    universityLogo: "https://images.squarespace-cdn.com/content/v1/57713a8e2994cae381dd86fe/1510405002757-FP2W9HV8ZA0HP2IF3O6A/SOA-PNG.png",
  },
];

const staggerContainer = {
  hidden: {},
  visible: {
    transition: { staggerChildren: 0.1 },
  },
};

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const TestimonialsContent = () => (
  <motion.section
    className="max-w-6xl mx-auto px-6 py-16"
    initial={{ opacity: 0 }}
    whileInView={{ opacity: 1 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true, amount: 0.1 }}
  >
    <ScrollReveal>
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-700 to-black">
          Student Testimonials
        </h2>
        <div className="h-1 w-24 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-6"></div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Hear from our students who transformed their careers through our
          comprehensive programs across all domains.
        </p>
      </div>
    </ScrollReveal>

    <motion.div
      className="relative"
      variants={staggerContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      <Swiper
        modules={[Pagination, Navigation, Autoplay]}
        spaceBetween={30}
        slidesPerView={1}
        pagination={{
          clickable: true,
          bulletClass: "swiper-pagination-bullet !bg-green-500",
          bulletActiveClass: "swiper-pagination-bullet-active !bg-green-400",
        }}
        navigation={{
          nextEl: ".testimonial-swiper-button-next",
          prevEl: ".testimonial-swiper-button-prev",
        }}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        loop={true}
        className="testimonials-swiper"
      >
        {testimonials.map((testimonial, idx) => (
          <SwiperSlide key={idx}>
            <motion.div
              className="bg-gradient-to-br from-black to-green-900 text-white rounded-xl shadow-xl p-6 sm:p-8 relative border border-green-500/20 max-w-4xl mx-auto"
              variants={fadeInUp}
              custom={idx}
              whileHover={{
                y: -10,
                boxShadow:
                  "0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1)",
                borderColor: "rgba(74, 222, 128, 0.4)",
              }}
              transition={{ type: "spring", stiffness: 300, damping: 15 }}
            >
              <motion.div
                className="absolute -top-3 sm:-top-4 left-4 sm:left-6 text-green-400 text-4xl sm:text-5xl"
                initial={{ opacity: 0, y: -10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + idx * 0.1 }}
                viewport={{ once: true }}
              >
                {`"`}
              </motion.div>

              <div className="pt-4 sm:pt-6">
                {/* Testimonial Title */}
                <motion.h3
                  className="text-green-400 font-bold text-lg sm:text-xl mb-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 + idx * 0.1 }}
                  viewport={{ once: true }}
                >
                  {testimonial.title}
                </motion.h3>

                {/* Testimonial Text */}
                <motion.p
                  className="text-gray-200 mb-6 sm:mb-8 text-sm sm:text-base leading-relaxed"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ delay: 0.4 + idx * 0.1 }}
                  viewport={{ once: true }}
                >
                  {testimonial.text}
                </motion.p>

                {/* Student Info */}
                <motion.div
                  className="flex flex-col sm:flex-row sm:items-center sm:justify-between border-t border-green-500/30 pt-6"
                  initial={{ y: 20, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 + idx * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-center mb-4 sm:mb-0">
                    <motion.div
                      className="bg-green-900 text-green-400 border border-green-500/30 rounded-full w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center text-lg sm:text-xl font-bold"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                      {testimonial.name.charAt(0)}
                    </motion.div>
                    <div className="ml-4">
                      <h4 className="font-bold text-green-400 text-base sm:text-lg">
                        {testimonial.name}
                      </h4>
                      <p className="text-gray-400 text-sm">
                        {testimonial.course} | {testimonial.batch}
                      </p>
                    </div>
                  </div>

                  {/* University Info */}
                  <div className="flex items-center">
                    {testimonial.universityLogo && (
                      <div className="w-8 h-8 sm:w-10 sm:h-10 mr-3 relative">
                        <Image
                          src={testimonial.universityLogo}
                          alt={`${testimonial.university} logo`}
                          fill
                          className="object-contain"
                          onError={(e) => {
                            // Fallback if logo doesn't exist
                            e.currentTarget.style.display = "none";
                          }}
                        />
                      </div>
                    )}
                    <p className="text-gray-300 text-sm font-medium">
                      {testimonial.university}
                    </p>
                  </div>
                </motion.div>
              </div>

              {/* Star Rating */}
              {/* <motion.div
                className="absolute right-4 sm:right-6 bottom-4 sm:bottom-6 flex"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: 0.6 + idx * 0.1 }}
                viewport={{ once: true }}
              >
                {[...Array(5)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ delay: 0.7 + idx * 0.1 + i * 0.05 }}
                    viewport={{ once: true }}
                  >
                    <FaStar className="text-yellow-400 text-xs sm:text-base mx-0.5" />
                  </motion.div>
                ))}
              </motion.div> */}
            </motion.div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Navigation Buttons */}
      <div className="testimonial-swiper-button-prev absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-green-500 hover:bg-green-600 text-white rounded-full w-10 h-10 flex items-center justify-center cursor-pointer transition-colors duration-300">
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
        </svg>
      </div>
      <div className="testimonial-swiper-button-next absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-green-500 hover:bg-green-600 text-white rounded-full w-10 h-10 flex items-center justify-center cursor-pointer transition-colors duration-300">
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </div>
    </motion.div>

    {/* Custom Swiper Styles */}
    <style jsx>{`
      .testimonials-swiper .swiper-pagination {
        bottom: -50px !important;
      }
      .testimonials-swiper .swiper-pagination-bullet {
        background: #10b981 !important;
        opacity: 0.5;
      }
      .testimonials-swiper .swiper-pagination-bullet-active {
        background: #059669 !important;
        opacity: 1;
      }
    `}</style>
  </motion.section>
);

export default TestimonialsContent;
