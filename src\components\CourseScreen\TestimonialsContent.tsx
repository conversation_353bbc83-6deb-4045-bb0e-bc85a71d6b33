import { motion } from "framer-motion";
import { FaStar } from "react-icons/fa";
import ScrollReveal from "@/components/Common/ScrollReveal";
import Image from "next/image";
import { useEffect, useState } from "react";

interface Testimonial {
  title: string;
  text: string;
  name: string;
  position: string;
  course: string;
  batch: string;
  university: string;
  universityLogo?: string;
}

const testimonials: Testimonial[] = [
  {
    title: "A Transformative Full Stack Web Development Journey",
    text: "My two-month internship with GrowGrid (https://growgrid.co.in/) was an extraordinary journey of growth, learning, and skill-building in End-to-End Full Stack Web Development. This immersive program opened the door to the dynamic world of front-end and back-end development, empowering me to refine my technical expertise and deepen my understanding of full-stack projects. Throughout the internship, I gained hands-on experience in crafting intricate designs, performing simulations, and mastering essential tools like HTML5, Node.js, and hosting platforms such as Vercel, Netlify, and Heroku. These practical insights have equipped me with a strong foundation to tackle future projects and advance my career in web development. I'm incredibly grateful for the unwavering support of GrowGrid's dedicated instructors and mentors, whose guidance made this experience truly transformative. This internship has not only honed my technical skills but also ignited a passion for creating innovative, impactful solutions. I highly recommend this program to any aspiring developer eager to take their skills to the next level!",
    name: "Abhisek A Rama",
    position: "Student",
    course: "END TO END FULL STACK WEB DEVELOPMENT",
    batch: "May 2025 Batch",
    university: "SRM UNIVERSITY",
    universityLogo: "https://upload.wikimedia.org/wikipedia/en/thumb/7/7a/SRM_Institute_of_Science_and_Technology_Logo.svg/1200px-SRM_Institute_of_Science_and_Technology_Logo.svg.png",
  },
  {
    title: "Transforming Data into Life-Saving Insights",
    text: "My Machine Learning internship with GrowGrid and Personify was an exhilarating and transformative experience! Working on a live project focused on predicting diabetes in patients, I had the opportunity to dive deep into algorithms like SVM and Naive Bayes, rigorously testing them to identify the most accurate models. This hands-on journey not only sharpened my technical expertise but also ignited a profound passion for leveraging AI to revolutionize healthcare. The program equipped me with practical skills that have boosted my confidence and preparedness for job interviews, inspiring me to pursue exciting opportunities in data analytics. The guidance from GrowGrid’s exceptional mentors was instrumental in making this experience unforgettable. I wholeheartedly recommend this program to anyone eager to harness the power of machine learning for meaningful impact!",
    name: "K Savita",
    position: "Student",
    course: "Ai Powered MACHINE LEARNING",
    batch: "May 2025 Batch",
    university: "Narsimha Reddy Engineering College",
    universityLogo: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSdJPvQIqSthL___1U-0_5u4W0-PtVYdYPM5g&s",
  },
  {
    title: "A Game-Changing Data Science Journey",
    text: "My Data Science internship with GrowGrid, in collaboration with tools like Google Colab, AWS, Kaggle, TensorFlow, and PyTorch, was an unforgettable and transformative experience. Working on a live project, I gained hands-on expertise with Python, Pandas, and machine learning algorithms, bringing concepts I had only studied into real-world application. From cleaning and preparing datasets to building models and presenting actionable insights, this program gave me a comprehensive understanding of the data science pipeline. The opportunity to apply these skills during an internship with Personifwy was a highlight, allowing me to tackle real-world challenges and solidify my expertise. This experience has not only prepared me for a thriving career in data science but also fueled my passion for turning data into meaningful solutions. A heartfelt thank you to GrowGrid’s exceptional mentors for their guidance and support! I highly recommend GrowGrid’s program to anyone seeking authentic, hands-on exposure to data science. It’s the perfect launchpad for aspiring data scientists!",
    name: "Praveen Reddy",
    position: "Student",
    course: "APPLIED DATA SCIENCE",
    batch: "May 2025 Batch",
    university: "Veltech University",
    universityLogo: "https://upload.wikimedia.org/wikipedia/en/e/e9/Veltech_Rangarajan_Dr._Sagunthala_R%26D_Institute_of_Science_and_Technology_logo.png",
  },
  {
    title: "An Empowering Cyber Security Adventure",
    text: "Enrolling in GrowGrid’s Cyber Security live projects during my summer vacation as a second-year college student was a game-changing decision! This immersive program plunged me into the world of real-world cyber threats and defensive strategies, offering hands-on experience that sharpened my critical thinking and problem-solving skills. Through challenging projects, I gained practical insights into cybersecurity that transformed my understanding of the field. Earning a certificate at the end of the program was a proud moment, and it now strengthens my resume as I pursue a career in this dynamic industry. This experience has significantly boosted my confidence and made me a more competitive candidate in the job market. I wholeheartedly recommend GrowGrid’s Cyber Security program to anyone passionate about safeguarding the digital world. It’s an unparalleled opportunity to gain real-world skills and kickstart your career!",
    name: "Kanha Pattnaik",
    position: "Student",
    course: "CYBER SECURITY & RISK MANAGEMENT",
    batch: "May 2025 Batch",
    university: "KIIT University",
    universityLogo: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRYDXHve3wBvopRRbF6Ded0o1L94QfgDtzd-w&s",
  },
  {
    title: "From Novice to Confident Web Developer",
    text: "When I joined GrowGrid’s End-to-End Full Stack Web Development program, I was unsure and overwhelmed about front-end development. However, the program’s clear, step-by-step approach transformed learning into an engaging and rewarding experience. By the end, I had built a website I’m incredibly proud of a testament to the skills I gained! The hands-on projects and supportive mentorship provided by GrowGrid were instrumental in building my confidence and laying a strong foundation in web development. This journey not only equipped me with practical skills but also opened my eyes to the vast opportunities in the tech industry. I’m thrilled about where these newfound abilities will take me and highly recommend GrowGrid’s program to anyone eager to kickstart their tech career!",
    name: "Subham Sahoo",
    position: "Student",
    course: "END TO END FULL STACK WEB DEVELOPMENT",
    batch: "May 2025 Batch",
    university: "SOA ITER",
    universityLogo: "https://images.squarespace-cdn.com/content/v1/57713a8e2994cae381dd86fe/1510405002757-FP2W9HV8ZA0HP2IF3O6A/SOA-PNG.png",
  },
  {
    title: "A Transformative Digital Marketing Experience",
    text: "My journey with GrowGrid’s Digital Marketing for Business Transformation program was nothing short of exceptional, and I’m immensely grateful for this incredible opportunity. Under the expert guidance of my mentor Manpreet Kaur , I gained invaluable knowledge and practical skills that have reshaped my understanding of digital marketing. The hands-on approach and insightful mentorship provided a strong foundation, empowering me to confidently apply these skills in future endeavors. This experience has fueled my passion for driving business transformation through digital strategies. I wholeheartedly recommend GrowGrid’s program to anyone looking to excel in the dynamic world of digital marketing!",
    name: "Nabin Das",
    position: "Student",
    course: "Digital Marketing for Business Transformation",
    batch: "May 2025 Batch",
    university: "CUTM, BBSR",
    universityLogo: "https://upload.wikimedia.org/wikipedia/en/thumb/6/62/Centurion_University_of_Technology_and_Management_Logo.svg/1200px-Centurion_University_of_Technology_and_Management_Logo.svg.png",
  },
  {
    title: "A Transformative Digital Marketing Experience",
    text: "To be honest, I joined Grow Grid thinking it was just another online program. But it turned out to be way more than that. It felt like a real internship  from working on live projects to getting feedback like you’d get in an actual company environment. The mentors were super helpful and made sure we understood how the real tech world works. What stood out the most was how practical everything was  we weren’t just learning theory; we were actually building stuff. Looking back, I can confidently say that my investment was 100% worth it. This program gave me the skills, the confidence, and the exposure I needed. Huge thanks to the Grow Grid team for such a solid experience!",
    name: "Saurav Pattnaik",
    position: "Student",
    course: "Full Stack Web Development",
    batch: "May 2025 Batch",
    university: "SOA ITER",
    universityLogo: "https://images.squarespace-cdn.com/content/v1/57713a8e2994cae381dd86fe/1510405002757-FP2W9HV8ZA0HP2IF3O6A/SOA-PNG.png",
  },
  {
    title: "A Transformative Digital Marketing Experience",
    text: "When I joined the Digital Marketing program at Grow Grid, I expected a basic course with recorded videos and some assignments. But honestly, it turned out to be so much more. It wasn’t just a program  it felt like a proper internship experience. We worked on real campaigns, learned how businesses actually grow online, and got hands-on with tools that digital marketers use every day. The sessions were super engaging, and the mentors actually guided us like a team not just like students. What makes me even more proud is being part of the 1st edtech startup of Odisha. It feels amazing to say I was one of Grow Grid’s early consumers, and I genuinely learned something valuable here. If you're serious about learning digital marketing the right way  this is the place.",
    name: "Subhra Pattnaik",
    position: "Student",
    course: "Digital Marketing for Business Transformation",
    batch: "May 2025 Batch",
    university: "C.V. Raman Global University",
    universityLogo: "https://upload.wikimedia.org/wikipedia/en/6/6a/C._V._Raman_Global_University_Logo.png",
  },
  {
    title: "A Transformative Digital Marketing Experience",
    text: "I got the kind of structured classroom learning that my college wasn’t providing. The concepts were explained clearly, and everything was super practical  not just theory from slides. What helped me a lot was their customer support. Whenever I was stuck or had doubts, the team was quick to respond and actually cared about solving the issue. From my side, I’d easily give it a 5 out of 5. If you really want to understand how digital marketing works  not just the buzzwords  Grow Grid is the right place to start.",
    name: "Sahil Behera",
    position: "Student",
    course: "Digital Marketing for Business Transformation",
    batch: "May 2025 Batch",
    university: "SOA ITER",
    universityLogo: "https://images.squarespace-cdn.com/content/v1/57713a8e2994cae381dd86fe/1510405002757-FP2W9HV8ZA0HP2IF3O6A/SOA-PNG.png",
  },
];

// Split testimonials into 3 columns for vertical scrolling
const splitTestimonials = () => {
  const shuffled = [...testimonials].sort(() => Math.random() - 0.5);
  const third = Math.ceil(shuffled.length / 3);
  return [
    shuffled.slice(0, third),
    shuffled.slice(third, third * 2),
    shuffled.slice(third * 2)
  ];
};

const TestimonialCard = ({
  testimonial,
  index,
  onOpenModal
}: {
  testimonial: Testimonial;
  index: number;
  onOpenModal: (testimonial: Testimonial) => void;
}) => {
  return (
    <motion.div
        className="w-full bg-gradient-to-br from-black to-green-800 rounded-xl shadow-lg p-6 mb-6 border border-green-500/40 relative overflow-hidden cursor-pointer max-w-sm mx-auto"
        whileHover={{
          scale: 1.02,
          borderColor: "#22c55e"
        }}
        transition={{ type: "spring", stiffness: 300, damping: 15 }}
        onClick={() => onOpenModal(testimonial)}
      >
        {/* Background pattern overlay */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-[url('/api/placeholder/400/300')] bg-cover bg-center"></div>
        </div>
        
        {/* Quote mark */}
        <div className="text-green-400 text-3xl font-bold relative z-10">"</div>
        
        {/* Title */}
        <h3 className="text-green-300 font-bold text-lg mb-3 relative z-10 overflow-hidden" style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          textOverflow: 'ellipsis'
        }}>
          {testimonial.title}
        </h3>

        {/* Text */}
        <div className="text-gray-200 text-sm leading-relaxed mb-6 relative z-10">
          <div className="overflow-hidden" style={{
            display: '-webkit-box',
            WebkitLineClamp: 4,
            WebkitBoxOrient: 'vertical',
            textOverflow: 'ellipsis'
          }}>
            {testimonial.text}
          </div>
          {testimonial.text.length > 200 && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onOpenModal(testimonial);
              }}
              className="text-green-400 hover:text-green-300 text-xs font-semibold mt-2 underline"
            >
              Read More
            </button>
          )}
        </div>
        
        {/* Student Info */}
        <div className="border-t border-green-500/40 pt-3 mt-3 relative z-10">
          <div className="flex items-start justify-between gap-2">
            <div className="flex items-center flex-1 min-w-0">
              <div className="bg-green-500 text-white rounded-full w-10 h-10 flex items-center justify-center text-xs font-bold flex-shrink-0">
                {testimonial.name.charAt(0)}
              </div>
              <div className="ml-2 min-w-0 flex-1">
                <h4 className="font-semibold text-white text-xs truncate">
                  {testimonial.name}
                </h4>
                <p className="text-green-300 text-xs break-words leading-tight overflow-hidden" style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  textOverflow: 'ellipsis'
                }}>
                  {testimonial.course}
                </p>
              </div>
            </div>

            {/* University Logo */}
            {testimonial.universityLogo && (
              <div className="w-8 h-8 relative flex-shrink-0">
                <Image
                  src={testimonial.universityLogo}
                  alt={testimonial.university}
                  fill
                  className="object-contain"
                />
              </div>
            )}
          </div>
        </div>
        
        {/* Star Rating */}
        <div className="flex justify-center mt-3 relative z-10">
          {[...Array(5)].map((_, i) => (
            <FaStar key={i} className="text-yellow-400 text-xs mx-0.5" />
          ))}
        </div>
      </motion.div>
  );
};

const VerticalScrollingColumn = ({
  testimonials,
  direction,
  speed,
  onOpenModal
}: {
  testimonials: Testimonial[];
  direction: 'up' | 'down';
  speed: number;
  onOpenModal: (testimonial: Testimonial) => void;
}) => {
  const [isPaused, setIsPaused] = useState(false);
  const duplicatedTestimonials = [...testimonials, ...testimonials];
  const cardHeight = 320; // Reduced height to match smaller cards

  return (
    <div
      className="h-[700px] overflow-hidden relative"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      <motion.div
        className="flex flex-col"
        animate={isPaused ? {} : {
          y: direction === 'up'
            ? [0, -testimonials.length * cardHeight]
            : [-testimonials.length * cardHeight, 0]
        }}
        transition={{
          y: {
            repeat: Infinity,
            repeatType: "loop",
            duration: speed,
            ease: "linear",
          },
        }}
      >
        {duplicatedTestimonials.map((testimonial, index) => (
          <div key={`${testimonial.name}-${index}`} className="px-2 py-1">
            <TestimonialCard
              testimonial={testimonial}
              index={index}
              onOpenModal={onOpenModal}
            />
          </div>
        ))}
      </motion.div>
    </div>
  );
};

const TestimonialsContent = () => {
  const [testimonialGroups, setTestimonialGroups] = useState<Testimonial[][]>([]);
  const [isMobile, setIsMobile] = useState(false);
  const [selectedTestimonial, setSelectedTestimonial] = useState<Testimonial | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = (testimonial: Testimonial) => {
    setSelectedTestimonial(testimonial);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedTestimonial(null);
  };

  useEffect(() => {
    setTestimonialGroups(splitTestimonials());

    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <motion.section
      className="w-full py-16 bg-gray-50 overflow-hidden relative"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true, amount: 0.1 }}
    >
      <ScrollReveal>
        <div className="text-center mb-16 px-6 relative z-10">
          <h2 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-700 to-black">
            Student Testimonials
          </h2>
          <div className="h-1 w-24 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Hear from our students who transformed their careers through our
            comprehensive programs across all domains.
          </p>
        </div>
      </ScrollReveal>

      {/* Three Vertical Scrolling Columns */}
      <div className="max-w-7xl mx-auto px-4 relative z-10">
        {isMobile ? (
          // Single column for mobile
          <div className="w-full">
            <VerticalScrollingColumn
              testimonials={testimonials}
              direction="up"
              speed={25}
              onOpenModal={handleOpenModal}
            />
          </div>
        ) : (
          // Three columns for desktop
          <div className="grid grid-cols-3 gap-6">
            {testimonialGroups.map((group, index) => (
              <VerticalScrollingColumn
                key={index}
                testimonials={group}
                direction={index % 2 === 0 ? 'up' : 'down'}
                speed={20 + index * 3}
                onOpenModal={handleOpenModal}
              />
            ))}
          </div>
        )}
      </div>

      {/* Global Modal - Positioned above all content */}
      {isModalOpen && selectedTestimonial && (
        <div onClick={handleCloseModal} className="fixed inset-0 z-[99999] flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div
            onClick={(e) => e.stopPropagation()}
            className="bg-white rounded-lg px-4 py-4 shadow-xl w-[90vw] max-w-4xl max-h-[80vh] overflow-hidden flex flex-col relative"
          >
            {/* Close Button */}
            <button
              onClick={handleCloseModal}
              className="absolute top-4 right-4 z-10 bg-gray-100 hover:bg-gray-200 rounded-full w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-800 transition-colors"
            >
              ✕
            </button>

            <div className="overflow-y-auto flex-1 px-6 py-6">
              <div className="flex items-center mb-6">
                <div className="bg-green-500 text-white rounded-full w-16 h-16 flex items-center justify-center text-lg font-bold mr-4">
                  {selectedTestimonial.name.charAt(0)}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 text-xl">
                    {selectedTestimonial.name}
                  </h4>
                  <p className="text-green-600 text-base">
                    {selectedTestimonial.course} | {selectedTestimonial.batch}
                  </p>
                  <p className="text-gray-600 text-base">
                    {selectedTestimonial.university}
                  </p>
                </div>
              </div>

              <h3 className="text-2xl font-bold px-4 py-4 text-gray-900">
                {selectedTestimonial.title}
              </h3>

              <div className="text-gray-700 text-lg leading-relaxed mb-8 px-4 bg-gray-50 rounded-lg">
                "{selectedTestimonial.text}"
              </div>

              <div className="flex items-center justify-between bg-white p-4 rounded-lg border border-gray-200">
                <div className="flex items-center">
                  <span className="text-base text-gray-600 mr-6 font-medium">
                    {selectedTestimonial.university}
                  </span>
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <FaStar key={i} className="text-yellow-400 text-lg mx-1" />
                    ))}
                  </div>
                </div>
                {selectedTestimonial.universityLogo && (
                  <div className="w-12 h-12 relative">
                    <Image
                      src={selectedTestimonial.universityLogo}
                      alt={selectedTestimonial.university}
                      fill
                      className="object-contain"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </motion.section>
  );
};

export default TestimonialsContent;
