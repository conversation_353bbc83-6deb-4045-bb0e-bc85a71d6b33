"use client";
import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Config } from "@/config";
import { slugify } from "@/utils/helper";
import { useRouter } from "next/navigation";

// Handle menu hover and click interactions
interface MenuInteraction {
  menu: string;
}

interface SubmenuInteraction {
  submenu: string;
}

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const router = useRouter();

  const internships = [
    {
      id: 1,
      name: "End to End Full Stack Web Development Internship",
      icon: "/assets/icons/nav/full-stack.png",
    },
    {
      id: 2,
      name: "Applied Data Science Internship Program",
      icon: "/assets/icons/nav/data-science.png",
    },
    {
      id: 3,
      name: "Cyber Security & Risk Management Internship",
      icon: "/assets/icons/nav/cyber-security.png",
    },
    {
      id: 4,
      name: "Data Structures & Algorithms Internship",
      icon: "/assets/icons/nav/data-structure.png",
    },
    {
      id: 5,
      name: "Cloud Architecture and Computing Internship",
      icon: "/assets/icons/nav/cloud-architecture.png",
    },
    {
      id: 6,
      name: "VLSI Design Fundamentals with AutoCAD Internship",
      icon: "/assets/icons/nav/vlsi-autoCAD.png",
    },
    {
      id: 7,
      name: "UI/UX for Digital Transformation Internship",
      icon: "/assets/icons/nav/ui-ux.png",
    },
    {
      id: 8,
      name: "AI-Powered Machine Learning Internship",
      icon: "/assets/icons/nav/machine-leaning.png",
    },
    {
      id: 9,
      name: "Strategic Human Resources Internship",
      icon: "/assets/icons/nav/hr.png",
    },
    {
      id: 10,
      name: "Digital Marketing for Business Transformation Internship",
      icon: "/assets/icons/nav/digital-marketing.png",
    },
    // {
    //   id: 11,
    //   name: "Next-Gen AI Prompt Designers Internship (Optional)",
    //   icon: "/assets/icons/nav/ai.png",
    // },
  ];

  const capsules = [
    { id: 1, name: "Core Concepts of C++", icon: "/assets/icons/nav/c.png" },
    {
      id: 2,
      name: "Core Concepts of Java",
      icon: "/assets/icons/nav/java.png",
    },
    {
      id: 3,
      name: "Artificial Intelligence",
      icon: "/assets/icons/nav/ai.png",
    },
    {
      id: 4,
      name: "Data Structures & Algorithms",
      icon: "/assets/icons/nav/data-structure.png",
    },
    {
      id: 5,
      name: "Machine Learning with Data Science",
      icon: "/assets/icons/nav/machine-leaning.png",
    },
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current?.contains(event.target as Node) &&
        window.innerWidth >= 768 // Only handle click outside for desktop view
      ) {
        setActiveDropdown(null);
        setActiveSubmenu(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleMenuInteraction = (menu: MenuInteraction["menu"]) => {
    if (window.innerWidth >= 768) {
      // Desktop: hover behavior
      setActiveDropdown(menu);
    } else {
      // Mobile: toggle behavior
      if (activeDropdown === menu) {
        setActiveDropdown(null);
        setActiveSubmenu(null);
      } else {
        setActiveDropdown(menu);
      }
    }
  };

  const handleSubmenuInteraction = (submenu: SubmenuInteraction["submenu"]) => {
    if (window.innerWidth >= 768) {
      // Desktop: hover behavior
      setActiveSubmenu(submenu);
    } else {
      // Mobile: toggle behavior
      setActiveSubmenu(activeSubmenu === submenu ? null : submenu);
    }
  };

  const handleRedirect = () => {
    router.push("/contact");
  };

  return (
    <header className="sticky top-0 inset-x-0 z-50 w-full">
      {/* On Nav Top */}
      <div className="text-white bg-green-800 hover:bg-green-700 hover:cursor-pointer py-4 text-center">
        <span className="">
          Need Help? Talk to us at {Config.CONTACT_NO} or{" "}
          <button onClick={handleRedirect} className="font-semibold border-b-2">
            Request a Call
          </button>
        </span>
      </div>
      <nav className="w-full px-6 md:px-16 py-2 bg-white">
        <div className="max-w-7xl mx-auto">
          {/* Three-column layout for desktop */}
          <div className="flex justify-between items-center">
            {/* Logo column - left aligned */}
            <div className="flex items-center justify-center md:justify-start w-1/3 md:w-1/4 h-full">
              <Link href="/">
                <div className="flex items-center justify-center group transition-all duration-300">
                  <img
                    src="/assets/images/logo.svg" // Replace with actual image path
                    alt="Logo"
                    className="w-[160px] md:w-[200px] h-auto max-h-[65px] md:max-h-[80px] my-auto"
                    style={{ minWidth: "160px" }}
                  />
                </div>
              </Link>
            </div>

            {/* Toggle button for mobile */}
            <div className="md:hidden">
              <button
                onClick={() => {
                  setIsOpen(!isOpen);
                  if (isOpen) {
                    // Reset dropdown states when closing the menu
                    setActiveDropdown(null);
                    setActiveSubmenu(null);
                  }
                }}
                className="text-gray-800 dark:text-white focus:outline-none bg-gray-100 dark:bg-gray-800 p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition duration-300"
                aria-label="Toggle menu"
              >
                <div className="w-6 flex flex-col items-center justify-center">
                  <span
                    className={`block w-5 h-0.5 bg-current transition-all duration-300 ${
                      isOpen ? "rotate-45 translate-y-1.5" : ""
                    }`}
                  ></span>
                  <span
                    className={`block w-5 h-0.5 bg-current my-1 transition-all duration-300 ${
                      isOpen ? "opacity-0" : ""
                    }`}
                  ></span>
                  <span
                    className={`block w-5 h-0.5 bg-current transition-all duration-300 ${
                      isOpen ? "-rotate-45 -translate-y-1.5" : ""
                    }`}
                  ></span>
                </div>
              </button>
            </div>

            {/* Navigation links column - center aligned */}
            <div className="hidden md:flex items-center justify-center w-2/4 text-gray-800 text-lg">
              <div className="flex items-center space-x-8">
                <Link
                  href="/"
                  className="nav-link relative overflow-hidden group"
                >
                  <span className="font-medium hover:text-[#59e602] dark:hover:text-[#59e602] transition duration-300">
                    Home
                  </span>
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#59e602] group-hover:w-full transition-all duration-300"></span>
                </Link>

                {/* Courses dropdown */}
                <div className="relative group" ref={dropdownRef}>
                  <button
                    className="flex items-center font-medium hover:text-[#59e602] dark:hover:text-[#59e602] transition duration-300 group"
                    onMouseEnter={() => handleMenuInteraction("courses")}
                    onClick={() => handleMenuInteraction("courses")}
                    aria-expanded={activeDropdown === "courses"}
                  >
                    Courses
                    <svg
                      className={`w-4 h-4 ml-1 transition-transform duration-300 ${
                        activeDropdown === "courses" ? "rotate-180" : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      ></path>
                    </svg>
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#59e602] group-hover:w-full transition-all duration-300"></span>
                  </button>

                  {activeDropdown === "courses" && (
                    <div
                      className="absolute left-0 mt-2 w-[800px] bg-white dark:bg-gray-800 text-sm shadow-xl rounded-lg py-2 z-10 opacity-0 translate-y-2 animated-dropdown flex"
                      onMouseLeave={() =>
                        window.innerWidth >= 768 && setActiveDropdown(null)
                      }
                      style={{ animation: "fadeIn 0.3s forwards" }}
                    >
                      {/* Categories Menu */}
                      <div className="w-1/3 border-r border-gray-200 dark:border-gray-700">
                        <div
                          className={`px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 flex justify-between items-center cursor-pointer rounded-md mx-1 transition-colors duration-200 ${
                            activeSubmenu === "internships" || !activeSubmenu
                              ? "bg-gray-100 dark:bg-gray-700"
                              : ""
                          }`}
                          onMouseEnter={() =>
                            handleSubmenuInteraction("internships")
                          }
                          onClick={() =>
                            handleSubmenuInteraction("internships")
                          }
                        >
                          <span className="text-gray-800 dark:text-gray-200 font-medium">
                            Industry Specialised Domains
                          </span>
                          <svg
                            className="w-4 h-4 text-gray-600 dark:text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M9 5l7 7-7 7"
                            ></path>
                          </svg>
                        </div>

                        <div
                          className={`px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 flex justify-between items-center cursor-pointer rounded-md mx-1 transition-colors duration-200 ${
                            activeSubmenu === "capsules"
                              ? "bg-gray-100 dark:bg-gray-700"
                              : ""
                          }`}
                          onMouseEnter={() =>
                            handleSubmenuInteraction("capsules")
                          }
                          onClick={() => handleSubmenuInteraction("capsules")}
                        >
                          <span className="text-gray-800 dark:text-gray-200 font-medium">
                            Capsule Domains
                          </span>
                          <svg
                            className="w-4 h-4 text-gray-600 dark:text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M9 5l7 7-7 7"
                            ></path>
                          </svg>
                        </div>
                      </div>

                      {/* Content Area */}
                      <div className="w-2/3 p-4">
                        {/* Internships content */}
                        {(activeSubmenu === "internships" ||
                          !activeSubmenu) && (
                          <div>
                            <h3 className="text-[#59E602] font-semibold mb-4">
                              Internship Programs
                            </h3>
                            <div className="grid grid-cols-2 gap-4">
                              {internships.map((internship) => (
                                <Link
                                  key={internship.id}
                                  href={`/courses/${slugify(internship?.name)}`}
                                  className="hover:bg-gray-100 dark:hover:bg-gray-700 p-2 rounded-md transition-colors duration-200"
                                >
                                  <div className="flex items-center">
                                    <img
                                      src={internship.icon}
                                      alt={internship.name}
                                      className="w-6 h-6 mr-2 object-contain"
                                    />
                                    <span className="text-gray-800 dark:text-gray-200">
                                      {internship.name}
                                    </span>
                                  </div>
                                </Link>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Capsules content */}
                        {activeSubmenu === "capsules" && (
                          <div>
                            <h3 className="text-[#59E602] font-semibold mb-4">
                              Specialized Learning Capsules
                            </h3>
                            <div className="grid grid-cols-2 gap-4">
                              {capsules.map((capsule) => (
                                <Link
                                  key={capsule.id}
                                  // href={`/capsule/${capsule.id}`}
                                  href={`/courses/${slugify(capsule?.name)}`}
                                  className="hover:bg-gray-100 dark:hover:bg-gray-700 p-2 rounded-md transition-colors duration-200"
                                >
                                  <div className="flex items-center">
                                    <img
                                      src={capsule.icon}
                                      alt={capsule.name}
                                      className="w-6 h-6 mr-2 object-contain"
                                    />
                                    <span className="text-gray-800 dark:text-gray-200">
                                      {capsule.name}
                                    </span>
                                  </div>
                                </Link>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <Link
                  href="/about"
                  className="nav-link relative overflow-hidden group"
                >
                  <span className="font-medium hover:text-[#59e602] dark:hover:text-[#59e602] transition duration-300">
                    About Us
                  </span>
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#59e602] group-hover:w-full transition-all duration-300"></span>
                </Link>

                <Link
                  href="/contact"
                  className="nav-link relative overflow-hidden group"
                >
                  <span className="font-medium hover:text-[#59e602] dark:hover:text-[#59e602] transition duration-300">
                    Contact Us
                  </span>
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#59e602] group-hover:w-full transition-all duration-300"></span>
                </Link>
              </div>
            </div>

            {/* Auth buttons column - right aligned */}
            <div className="hidden md:flex items-center justify-end w-1/4">
              <div className="flex space-x-3">
                {/* <Link
                  href="/login"
                  className="px-4 py-2 border border-white text-white rounded-md hover:bg-white hover:text-black transition-all duration-300 font-medium"
                >
                  Sign In
                </Link> */}
                {/* <Link
                  href="/signup"
                  className="px-4 py-2 bg-[#1A803D] text-white rounded-md hover:bg-[#1A803D] transition-all duration-300 font-medium"
                >
                  Sign Up
                </Link> */}
              </div>
            </div>
          </div>

          {/* Mobile menu */}
          <div
            className={`md:hidden mt-4 bg-white dark:bg-gray-900 rounded-lg shadow-inner overflow-hidden transition-all duration-300 ${
              isOpen ? "max-h-[80vh] opacity-100" : "max-h-0 opacity-0"
            }`}
            style={{
              transitionProperty: "max-height, opacity",
            }}
          >
            <div className="flex flex-col py-3 max-h-[calc(80vh-2rem)] overflow-y-auto">
              <Link
                href="/"
                className="px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition duration-200"
                onClick={() => setIsOpen(false)}
              >
                Home
              </Link>

              {/* Mobile Courses Dropdown */}
              <div className="relative">
                <button
                  className="w-full text-left px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition duration-200 flex justify-between items-center"
                  onClick={() => handleMenuInteraction("courses")}
                >
                  Courses
                  <svg
                    className={`w-4 h-4 transition-transform duration-300 ${
                      activeDropdown === "courses" ? "rotate-180" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>

                <div
                  className={`bg-gray-50 dark:bg-gray-800 rounded-md my-1 mx-2 overflow-hidden transition-all duration-300 ${
                    activeDropdown === "courses"
                      ? "max-h-screen opacity-100"
                      : "max-h-0 opacity-0"
                  }`}
                  style={{
                    transitionProperty: "max-height, opacity",
                  }}
                >
                  <div>
                    <button
                      className="w-full text-left px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition duration-200 flex justify-between items-center"
                      onClick={() => handleSubmenuInteraction("internships")}
                    >
                      Industry Specialised Domains
                      <svg
                        className={`w-4 h-4 transition-transform duration-300 ${
                          activeSubmenu === "internships" ? "rotate-90" : ""
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9 5l7 7-7 7"
                        ></path>
                      </svg>
                    </button>

                    <div
                      className={`bg-gray-100 dark:bg-gray-700 rounded-md my-1 mx-2 overflow-hidden transition-all duration-300 ${
                        activeSubmenu === "internships"
                          ? "max-h-screen opacity-100"
                          : "max-h-0 opacity-0"
                      }`}
                      style={{
                        transitionProperty: "max-height, opacity",
                      }}
                    >
                      {internships.map((internship) => (
                        <Link
                          key={internship.id}
                          href={`/courses/${slugify(internship?.name)}`}
                          className="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition duration-200"
                          onClick={() => setIsOpen(false)}
                        >
                          <div className="flex items-center">
                            <img
                              src={internship.icon}
                              alt={internship.name}
                              className="w-5 h-5 mr-2 object-contain"
                            />
                            <span>{internship.name}</span>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>

                  <div>
                    <button
                      className="w-full text-left px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition duration-200 flex justify-between items-center"
                      onClick={() => handleSubmenuInteraction("capsules")}
                    >
                      Capsule Domains
                      <svg
                        className={`w-4 h-4 transition-transform duration-300 ${
                          activeSubmenu === "capsules" ? "rotate-90" : ""
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9 5l7 7-7 7"
                        ></path>
                      </svg>
                    </button>

                    <div
                      className={`bg-gray-100 dark:bg-gray-700 rounded-md my-1 mx-2 overflow-hidden transition-all duration-300 ${
                        activeSubmenu === "capsules"
                          ? "max-h-screen opacity-100"
                          : "max-h-0 opacity-0"
                      }`}
                      style={{
                        transitionProperty: "max-height, opacity",
                      }}
                    >
                      {capsules.map((capsule) => (
                        <Link
                          key={capsule.id}
                          href={`/courses/${slugify(capsule?.name)}`}
                          className="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition duration-200"
                          onClick={() => setIsOpen(false)}
                        >
                          <div className="flex items-center">
                            <img
                              src={capsule.icon}
                              alt={capsule.name}
                              className="w-5 h-5 mr-2 object-contain"
                            />
                            <span>{capsule.name}</span>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <Link
                href="/about"
                className="px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition duration-200"
                onClick={() => setIsOpen(false)}
              >
                About Us
              </Link>

              <Link
                href="/contact"
                className="px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition duration-200"
                onClick={() => setIsOpen(false)}
              >
                Contact Us
              </Link>

              {/* Mobile Sign In/Sign Up */}
              {/* <div className="flex flex-col space-y-2 mt-3 px-4 py-2 border-t border-gray-200 dark:border-gray-700">
                <Link
                  href="/login"
                  className="py-2 px-4 border border-[#59E602] text-[#59E602] text-center rounded-md hover:bg-[#59E602] hover:text-white transition-all duration-300"
                  onClick={() => setIsOpen(false)}
                >
                  Sign In
                </Link>
                <Link
                  href="/signup"
                  className="py-2 px-4 bg-[#59E602] text-white text-center rounded-md hover:bg-[#4bd000] transition-all duration-300"
                  onClick={() => setIsOpen(false)}
                >
                  Sign Up
                </Link>
              </div> */}
            </div>
          </div>
        </div>
      </nav>

      {/* Add CSS Animation */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animated-dropdown {
          animation-fill-mode: forwards;
        }
      `}</style>
    </header>
  );
}
