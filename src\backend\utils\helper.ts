export type Params = Promise<{ id: string; slug: string }>;

import { z } from "zod";

export const RequestCallSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .regex(
      /^[A-Za-z\s]{2,50}$/,
      "Name should only contain letters and spaces, between 2-50 characters"
    ),
  email: z.string().email("Invalid email format"),
  phone: z
    .string()
    .regex(
      /^(\+91[\-\s]?)?[6789]\d{9}$/,
      "Invalid Indian phone number. Must be 10 digits starting with 6-9, optionally with +91"
    ),
  details: z.string().optional(),
  experience: z.string().optional(),
  course: z.string().optional(),
  courseId: z.string().optional(),
  type: z.enum(["call", "apply-course"]).default("call"),
});
