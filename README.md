# Grow Grid

Grow Grid is Odisha's first organized Ed-Tech platform, helping students bridge the gap between where they are and where they want to be by offering transparent, future-ready education.

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

## Environment Setup

Before running the application, make sure to set up the required environment variables:

1. Create a `.env.local` file in the root directory
2. Add the following variables:

```
# Database URLs
DB_PRODUCTION=mongodb+srv://...
DB_TEST=mongodb+srv://...
DB_DEVELOPMENT=mongodb+srv://...
DB_LOCAL=mongodb://localhost:27017/growgrid

# Authentication
JWT_SECRET=your_jwt_secret_key

# API URL
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Razorpay (Payment Gateway)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret

# Site URL
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

You can run `npm run check-env` to verify your environment variables are set correctly.

## Tech Stack

- **Framework**: Next.js 15
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Database**: MongoDB with Mongoose
- **Authentication**: JWT
- **Payment Processing**: Razorpay
- **State Management**: Zustand
- **Form Validation**: Zod
- **Animations**: Framer Motion

## Project Structure

```
/src
  /app             # Next.js App Router
  /backend         # Backend API code
    /config        # Configuration files
    /lib           # Library code (DB connection, etc.)
    /models        # Mongoose models
  /components      # Reusable UI components
  /screens         # Page-level components
  /service         # API service functions
  /utils           # Utility functions
```

## Deployment

### Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

### Deploy with PM2

To deploy using PM2:

1. Clone the repository on your server
2. Run the build script: `./build.sh`
3. The script will:
   - Pull the latest changes
   - Install dependencies
   - Build the application
   - Restart the app using PM2

## Payment Integration

### Razorpay Setup

1. Create a Razorpay account at [razorpay.com](https://razorpay.com)
2. Get your API keys from the Dashboard
3. Add the keys to your environment variables:
   - `RAZORPAY_KEY_ID`
   - `RAZORPAY_KEY_SECRET`

### Razorpay Webhook Setup

To enable Razorpay webhooks:

1. Log in to your Razorpay Dashboard
2. Go to Settings > Webhooks
3. Click "Add New Webhook"
4. Enter your webhook URL: `https://yourdomain.com/api/payment/webhook`
5. Select the following events:
   - payment.authorized
   - payment.captured
   - payment.failed
   - refund.created
6. Generate a webhook secret and add it to your environment variables as `RAZORPAY_WEBHOOK_SECRET`
7. Click "Create Webhook"

Make sure your server is accessible from the internet for webhooks to work properly.

### Testing Webhooks Locally

For local development, you can use tools like ngrok to expose your local server to the internet:

1. Install ngrok: `npm install -g ngrok`
2. Start your Next.js development server: `npm run dev`
3. In a separate terminal, run: `ngrok http 3000`
4. Use the generated HTTPS URL from ngrok as your webhook URL in the Razorpay dashboard
5. Webhooks will now be forwarded to your local development server

### Webhook Security

Razorpay webhooks are secured using HMAC signatures. Our implementation verifies these signatures to ensure the webhook requests are authentic and come from Razorpay.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

Razorpay webhooks are secured using HMAC signatures. Our implementation verifies these signatures to ensure the webhook requests are authentic and come from Razorpay.
