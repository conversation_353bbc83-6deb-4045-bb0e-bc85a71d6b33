// middleware.ts
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import axios from "axios";

// Define paths and methods to protect
const protectedPaths = ["/api/admin"];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the request matches a protected path and method
  const isProtectedPath = protectedPaths.some((path) =>
    pathname.startsWith(path)
  );
  if (isProtectedPath) {
    const token = request.cookies.get("token")?.value;

    if (!token) {
      return NextResponse.json(
        { error: "Unauthorized: No token" },
        { status: 401 }
      );
    }
    try {
      const response = await axios.post(`${request.nextUrl.origin}/api/auth/verify`, { token });
      
      // Check if user has admin role
      const user = response.data.user;
      if (user.role !== 'admin') {
        return NextResponse.json(
          { error: "Forbidden: Admin access required" },
          { status: 403 }
        );
      }
      
      return NextResponse.next(); // Valid token and admin role
    } catch (err: unknown) {
      const error = err as Error;
      return NextResponse.json(
        { error: "Unauthorized: Invalid token" },
        { status: 401 }
      );
    }
  }

  return NextResponse.next();
}

// // This ensures the middleware only runs on relevant route prefixes (reduces performance overhead)
// export const config = {
//   matcher: ['/api/protected/:path*', '/api/admin/:path*'],
// };
