import { NextRequest, NextResponse } from "next/server";
import { Contact } from "../../../backend/models"; // Using the barrel file
import { connectDB } from "../../../backend/lib/mongodb";
import { contactSchema } from "../../../utils/contact";

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    const body = await request.json();

    // Validate the request body using Zod
    const result = contactSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation error",
          errors: result.error.errors,
        },
        { status: 400 }
      );
    }

    const newContact = await Contact.create(result.data);
    return NextResponse.json(
      { success: true, data: newContact },
      { status: 201 }
    );
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 400 }
    );
  }
}
