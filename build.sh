#!/bin/bash

# Exit immediately if any command exits with a non-zero status
set -e

# Pull the latest changes from the main branch
echo "Pulling latest changes from main..."
git pull origin main

# Clean up previous build
echo "Cleaning up previous build..."
rm -rf .next

# Install dependencies
echo "Installing dependencies..."
npm install

# Build the application
echo "Building the application..."
npm run build

# Restart the app using PM2
echo "Restarting the app with PM2..."
pm2 restart growgrid

echo "✅ Build and restart completed successfully!"
