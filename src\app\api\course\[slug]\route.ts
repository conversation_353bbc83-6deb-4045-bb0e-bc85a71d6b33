import { NextRequest, NextResponse } from "next/server";
import { Course } from "../../../../backend/models"; // Using the barrel file
import { connectDB } from "../../../../backend/lib/mongodb";
import { Params } from "../../../../backend/utils/helper";

export async function GET(
  Request: NextRequest,
  { params }: { params: Params }
) {
  try {
    await connectDB();
    // Check if we're getting a specific course by ID
    const slug = (await params)?.slug;

    if (!slug) {
      return NextResponse.json(
        { success: false, message: "Course ID is required" },
        { status: 400 }
      );
    }

    const course = await Course.findOne({slug}, { __v: 0 });
    if (!course) {
      return NextResponse.json(
        { success: false, message: "Course not found" },
        { status: 404 }
      );
    }
    return NextResponse.json({ success: true, data: course });
  } catch (error: unknown) {
    console.log(error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: errorMessage.includes("timeout") ? 504 : 500 }
    );
  }
}

