"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { flipCard } from "@/utils/advancedAnimations";

interface FlipCardProps {
  frontContent: React.ReactNode;
  backContent: React.ReactNode;
  className?: string;
  width?: string | number;
  height?: string | number;
  flipOnHover?: boolean;
  flipOnClick?: boolean;
}

const FlipCard: React.FC<FlipCardProps> = ({
  frontContent,
  backContent,
  className = "",
  width = "100%",
  height = "100%",
  flipOnHover = false,
  flipOnClick = true,
}) => {
  const [isFlipped, setIsFlipped] = useState(false);
  
  const handleFlip = () => {
    if (flipOnClick) {
      setIsFlipped(!isFlipped);
    }
  };
  
  const handleHoverStart = () => {
    if (flipOnHover) {
      setIsFlipped(true);
    }
  };
  
  const handleHoverEnd = () => {
    if (flipOnHover) {
      setIsFlipped(false);
    }
  };

  return (
    <div
      className={`relative ${className}`}
      style={{ 
        width, 
        height, 
        perspective: "1000px" 
      }}
      onClick={handleFlip}
      onMouseEnter={handleHoverStart}
      onMouseLeave={handleHoverEnd}
    >
      <div
        className="relative w-full h-full"
        style={{ transformStyle: "preserve-3d" }}
      >
        {/* Front side */}
        <motion.div
          className="absolute w-full h-full backface-hidden rounded-lg overflow-hidden"
          animate={isFlipped ? "back" : "front"}
          variants={flipCard}
          style={{ 
            backfaceVisibility: "hidden",
            zIndex: isFlipped ? 0 : 1,
          }}
        >
          {frontContent}
        </motion.div>
        
        {/* Back side */}
        <motion.div
          className="absolute w-full h-full backface-hidden rounded-lg overflow-hidden"
          animate={isFlipped ? "front" : "back"}
          variants={flipCard}
          style={{ 
            backfaceVisibility: "hidden",
            transform: "rotateY(180deg)",
            zIndex: isFlipped ? 1 : 0,
          }}
        >
          {backContent}
        </motion.div>
      </div>
      
      {/* Add CSS for backface visibility since it's not always supported in all browsers */}
      <style jsx global>{`
        .backface-hidden {
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
        }
      `}</style>
    </div>
  );
};

export default FlipCard;
