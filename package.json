{"name": "grow-grid", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:prod": "cross-env NODE_ENV=production next dev --turbopack", "build": "cross-env NODE_ENV=production next build", "start": "cross-env NODE_ENV=production next start", "lint": "echo 'ESLint disabled'", "check-env": "node scripts/check-env.js"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "dotenv": "^16.3.1", "framer-motion": "^12.6.3", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.487.0", "mongoose": "^8.14.0", "motion": "^12.6.3", "next": "^15.3.1", "next-auth": "^4.24.11", "razorpay": "^2.9.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-modal": "^3.16.3", "swiper": "^11.2.8", "zod": "^3.24.3", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-modal": "^3.16.3", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "^15.3.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}