"use client";

import { motion } from "framer-motion";
import { fadeInUp, buttonTap } from "@/utils/animations";

type CardReadMoreProps = {
  imageSrc?: string | null;
  title?: string;
  description?: string | React.ReactNode;
  showReadMore?: boolean;
  mainClassname?: string;
  subClassname?: string;
  titleClassname?: string;
  delay?: number;
};

const CardReadMore = ({
  title = "",
  description = "",
  imageSrc = null,
  showReadMore = true,
  mainClassname = "",
  subClassname = "",
  titleClassname = "",
  delay = 0,
}: CardReadMoreProps) => {
  return (
    <motion.div
      className={`${mainClassname} max-w-sm bg-white pb-4 sm:pb-5 rounded-lg sm:rounded-xl shadow-md sm:shadow-lg relative flex flex-col items-center w-full mx-auto`}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.3 }}
      variants={fadeInUp}
      transition={{ delay: delay * 0.1 }}
      whileHover={{ scale: 1.02, boxShadow: "0 15px 20px -5px rgba(0, 0, 0, 0.1), 0 8px 8px -5px rgba(0, 0, 0, 0.04)" }}
    >
      <motion.div
        className="relative w-full h-32 xs:h-36 sm:h-48 md:h-56 rounded-t-lg sm:rounded-t-xl overflow-hidden"
        whileHover={{ scale: 1.03 }}
        transition={{ duration: 0.3 }}
      >
        <img
          src={imageSrc ?? "https://images.unsplash.com/photo-1509062522246-3755977927d7?q=80&w=3032&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"}
          alt="Classroom"
          className="w-full h-full object-cover"
        />
      </motion.div>
      <div className={`${subClassname} p-3 sm:p-4 md:p-6`}>
        <motion.h3
          className={`${titleClassname} text-base xs:text-lg sm:text-xl font-bold text-gray-900 text-center`}
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          {title}
        </motion.h3>
        <motion.div
          className="text-gray-600 text-[10px] xs:text-xs sm:text-sm mt-1 sm:mt-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {description}
        </motion.div>
      </div>
      {showReadMore && (
        <motion.button
          className="absolute -bottom-3 sm:-bottom-4 bg-green-600 text-white px-2 sm:px-3 md:px-4 py-1 sm:py-1.5 md:py-2 rounded-full text-[10px] xs:text-xs sm:text-sm text-center font-semibold hover:bg-green-700"
          whileHover={{ scale: 1.03, backgroundColor: "#166534" }}
          whileTap="tap"
          variants={buttonTap}
        >
          READ MORE
        </motion.button>
      )}
    </motion.div>
  );
};

export default CardReadMore;
