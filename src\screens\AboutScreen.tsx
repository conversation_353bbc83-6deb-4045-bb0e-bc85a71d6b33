"use client";
import React from "react";
import Header from "@/components/Common/Header";
import ImageSection from "@/components/Common/ImageSection";
import { motion } from "framer-motion";
import { staggerContainer } from "@/utils/animations";

export default function AboutScreen() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white py-20">
      <div className="container mx-auto px-4">
        <Header
          boldTitle={"About"}
          title={"Company"}
          isAboutUs={true}
          subtitle=""
        />

        <motion.div 
          className="max-w-6xl mx-auto mt-12 space-y-12"
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {/* Vision Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <ImageSection
              title="Vision"
              description={
                <div>
                  <span className="text-[10px] xs:text-xs md:text-sm text-gray-600">
                    To lead the training game by creating in-demand professionals
                    with skills that stand the test of time.
                  </span>
                  <ul className="list-disc px-3 sm:px-4 md:px-6 py-1 sm:py-2 text-[10px] xs:text-xs md:text-sm text-gray-600">
                    <li>Open doors for both techies and non-techies.</li>
                    <li>
                      Deliver a highly personalised, next-level learning experience.
                    </li>
                  </ul>
                </div>
              }
              leftImage="/assets/images/vision.png"
              imageAlt="Vision"
            />
          </motion.div>

          {/* Mission Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <ImageSection
              title="Mission"
              description={
                <div>
                  <span className="text-[10px] xs:text-xs md:text-sm text-gray-600">
                    Protecting your career, no matter how unpredictable the job
                    market gets, by equipping you with game-changing, future-ready
                    tech skills.
                  </span>
                  <ul className="list-disc px-3 sm:px-4 md:px-6 py-1 sm:py-2 text-[10px] xs:text-xs md:text-sm text-gray-600">
                    <li>Arm everyone with the knowledge to thrive.</li>
                    <li>
                      Crushing the myth that only IIT and IIM pros can rake in the big
                      bucks.
                    </li>
                  </ul>
                </div>
              }
              rightImage="/assets/images/mission.png"
              imageAlt="Mission"
            />
          </motion.div>

          {/* Our Story Section */}
          {/* <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-gradient-to-r from-green-50 to-gray-50 rounded-xl p-8"
          >
            <h2 className="text-2xl sm:text-3xl font-bold mb-6 bg-gradient-to-r from-green-700 to-green-900 text-transparent bg-clip-text">Our Story 🌱</h2>
            <div className="space-y-4 text-gray-700">
              <p>
                <b>GROW GRID</b> started with a simple idea: education should be accessible, practical, and future-ready. 
                Founded in Odisha, we quickly became the region's first organized Ed-Tech platform dedicated to bridging the gap 
                between traditional education and industry demands.
              </p>
              <p>
                What began as a small initiative has now grown into a movement that's transforming how students across India 
                approach their career development. We believe that quality education shouldn't be limited by geography or background.
              </p>
              <p>
                Our journey has been guided by a commitment to transparency, innovation, and student success. Every course, 
                every program, and every initiative we launch is designed with one goal in mind: to help you thrive in an 
                ever-changing job market.
              </p>
            </div>
          </motion.div> */}

          {/* Our Team Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="bg-gradient-to-r from-green-50 to-gray-50 rounded-xl p-8"
          >
            <h2 className="text-2xl sm:text-3xl font-bold mb-6 bg-gradient-to-r from-green-700 to-green-900 text-transparent bg-clip-text">Our Values 🌟</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                <h3 className="text-xl font-semibold text-green-700 mb-3">Innovation</h3>
                <p className="text-gray-600">We constantly evolve our teaching methods and curriculum to stay ahead of industry trends and provide cutting-edge education.</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                <h3 className="text-xl font-semibold text-green-700 mb-3">Inclusivity</h3>
                <p className="text-gray-600">We believe in creating opportunities for everyone, regardless of their background or previous experience.</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                <h3 className="text-xl font-semibold text-green-700 mb-3">Excellence</h3>
                <p className="text-gray-600">We strive for the highest standards in everything we do, from course content to student support.</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                <h3 className="text-xl font-semibold text-green-700 mb-3">Empowerment</h3>
                <p className="text-gray-600">We don't just teach skills; we empower students to take control of their careers and futures.</p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
