"use client";

import React from "react";
import { motion } from "framer-motion";
import { buttonTap } from "@/utils/animations";

type AnimatedButtonProps = {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  type?: "button" | "submit" | "reset";
  disabled?: boolean;
  variant?: "primary" | "secondary" | "outline";
};

const AnimatedButton = ({
  children,
  onClick,
  className = "",
  type = "button",
  disabled = false,
  variant = "primary",
}: AnimatedButtonProps) => {
  // Base styles for different variants
  const baseStyles = {
    primary: "bg-green-700 text-white hover:bg-green-800",
    secondary: "bg-purple-600 text-white hover:bg-purple-700",
    outline: "border-2 border-green-700 text-green-700 hover:bg-green-50",
  };

  return (
    <motion.button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`px-6 py-3 font-semibold rounded-lg transition duration-300 shadow-lg ${
        baseStyles[variant]
      } ${disabled ? "opacity-50 cursor-not-allowed" : ""} ${className}`}
      whileHover={!disabled ? { scale: 1.05 } : {}}
      whileTap={!disabled ? "tap" : {}}
      variants={buttonTap}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.button>
  );
};

export default AnimatedButton;
