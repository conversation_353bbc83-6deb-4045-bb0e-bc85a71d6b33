import { NextResponse } from "next/server";
import { connectDB } from "@/backend/lib/mongodb";
import RequestCall from "@/backend/models/RequestCall"; // Adjust the import path as needed
import { RequestCallSchema } from "@/backend/utils/helper";

export async function POST(request: Request) {
  try {
    await connectDB();

    // Parse the request body
    const body = await request.json();

    // Validate request body against schema
    const result = RequestCallSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation failed",
          error: result?.error?.issues,
        },
        { status: 400 }
      );
    }

    // Create new request call entry
    const newRequestCall = await RequestCall.create(result.data);

    return NextResponse.json(
      { success: true, data: newRequestCall },
      { status: 201 }
    );
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";

    if (errorMessage.includes("duplicate key error")) {
      return NextResponse.json(
        { success: false, message: "Email already exists" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: errorMessage.includes("timeout") ? 504 : 500 }
    );
  }
}
