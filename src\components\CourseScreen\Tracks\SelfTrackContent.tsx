import TrackButton from "@/components/Buttons/TrackButton";
import { usePlanStore } from "@/store/planStore";
import { redirectURL } from "@/utils/helper";
import { plansIds } from "@/utils/payment";
import { FaCheckCircle, FaTimes } from "react-icons/fa";

interface SelfTrackContentProps {
  setEnrollmentOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const SelfTrackContent: React.FC<SelfTrackContentProps> = ({
  setEnrollmentOpen,
}) => {
  const { setPlanSelected } = usePlanStore();
  // Function to handle enrollment button click
  const handleEnrollmentClick = () => {
    try {
      setPlanSelected(plansIds?.self);
      setEnrollmentOpen(false);
      window.open(redirectURL.lms, '_blank');
    } catch (error) {
      console.error("Error opening enrollment link:", error);
    }
  };

  return (
    <div className="p-6 sm:p-8 flex flex-col h-full">
      <div className="text-center mb-6">
        <h2 className="text-2xl sm:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-green-600">
          SELF TRACK
        </h2>
        <div className="h-1 w-20 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-4"></div>
        <p className="text-sm sm:text-base text-gray-300 mx-auto">
          Student-Friendly Training – Learn Anytime, Anywhere
        </p>
      </div>

      {/* Price */}
      <div className="text-center mb-8">
        <div className="text-lg text-green-400 font-semibold mb-2">
          Program Fee
        </div>
        <div className="text-4xl sm:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-white mb-2">
          ₹8,000
        </div>
        <div className="h-0.5 w-16 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-3"></div>
        <div className="text-gray-300 text-sm">
          One-time payment with lifetime access to course content
        </div>
      </div>

      {/* Features list */}
      <div className="space-y-4 flex-grow mb-8">
        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Flexible Learning, Designed for You
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Learn at your own pace with 24/7 access to high-quality video
              classes—anytime, anywhere.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Progress with Purpose
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Stay motivated through structured weekly milestones that help you
              build momentum and stay on track.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Ideal for Self-Starters
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Perfect for independent learners who value flexibility, with added
              support through weekly live doubt-clearing sessions.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Lifetime Access, Limitless Learning
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Enjoy unlimited access to 40–50 hours of recorded video content
              through our intuitive LMS—revisit lessons anytime for deeper
              mastery.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Hands-On Learning with Real Projects
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Apply your skills through one minor and one major project,
              designed to reinforce practical, real-world knowledge.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              Get Certified
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Receive an industry-recognized certificate upon successful course
              completion—boost your resume and showcase your achievement.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-green-400 mr-3 mt-1 flex-shrink-0">
            <FaCheckCircle />
          </div>
          <div>
            <p className="text-green-300 text-sm font-semibold">
              60-Day Skill-Building Journey
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Includes structured training sessions and real-world project
              execution for end-to-end learning.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-red-400 mr-3 mt-1 flex-shrink-0">
            <FaTimes />
          </div>
          <div>
            <p className="text-gray-400 text-sm font-semibold">
              1-on-1 Mentorship
            </p>
            <p className="text-gray-500 text-xs mt-1">
              Not available in Self Track.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-red-400 mr-3 mt-1 flex-shrink-0">
            <FaTimes />
          </div>
          <div>
            <p className="text-gray-400 text-sm font-semibold">
              Career-Ready Support
            </p>
            <p className="text-gray-500 text-xs mt-1">
              Resume building and interview preparation not included.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="text-red-400 mr-3 mt-1 flex-shrink-0">
            <FaTimes />
          </div>
          <div>
            <p className="text-gray-400 text-sm font-semibold">
              Earn While You Learn
            </p>
            <p className="text-gray-500 text-xs mt-1">
              Stipend opportunities not available in Self Track.
            </p>
          </div>
        </div>
      </div>

      {/* CTA Button */}
      {/* <div className="mt-auto text-center">
        <motion.button
          className="w-full bg-gradient-to-r from-green-600 to-green-800 hover:from-green-500 hover:to-green-700 text-white font-bold py-3 px-6 rounded-lg shadow-lg"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setEnrollmentOpen(true)}
        >
          Apply for Self Track
        </motion.button>
      </div> */}
      <TrackButton
        name="Enroll for Self Track"
        price={8000}
        onclick={handleEnrollmentClick}
      />
    </div>
  );
};

export default SelfTrackContent;
