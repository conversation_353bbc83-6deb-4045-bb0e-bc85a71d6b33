"use client";

import React, { useRef, useState } from "react";
import { motion, useMotionValue, useSpring, useTransform } from "framer-motion";

interface MagneticButtonProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  strength?: number;
  radius?: number;
  color?: string;
  glowColor?: string;
  type?: "button" | "submit" | "reset";
}

const MagneticButton: React.FC<MagneticButtonProps> = ({
  children,
  className = "",
  onClick,
  strength = 40,
  radius = 150,
  color = "bg-green-600",
  glowColor = "rgba(66, 220, 163, 0.7)",
  type = "button",
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [isHovered, setIsHovered] = useState(false);

  // Motion values for tracking mouse position
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  // Spring animations for smoother movement
  const springX = useSpring(mouseX, { stiffness: 150, damping: 15 });
  const springY = useSpring(mouseY, { stiffness: 150, damping: 15 });

  // Transform mouse position to button movement
  const moveX = useTransform(springX, [-radius, radius], [-strength, strength]);
  const moveY = useTransform(springY, [-radius, radius], [-strength, strength]);

  // Handle mouse move
  const handleMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!buttonRef.current || !isHovered) return;

    const rect = buttonRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // Calculate distance from center
    mouseX.set(e.clientX - centerX);
    mouseY.set(e.clientY - centerY);
  };

  // Reset on mouse leave
  const handleMouseLeave = () => {
    mouseX.set(0);
    mouseY.set(0);
    setIsHovered(false);
  };

  return (
    <motion.button
      ref={buttonRef}
      type={type}
      onClick={onClick}
      className={`relative px-6 py-3 font-semibold rounded-lg overflow-hidden ${color} ${className}`}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      style={{
        x: moveX,
        y: moveY,
      }}
      whileHover={{ scale: 1.05, boxShadow: `0px 0px 20px ${glowColor}` }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      whileTap={{ scale: 0.95 }}
    >
      {/* Button content */}
      <motion.span
        className="relative z-10"
        animate={{ scale: isHovered ? 1.05 : 1 }}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
      >
        {children}
      </motion.span>

      {/* Glow effect */}
      {isHovered && (
        <motion.div
          className="absolute inset-0 rounded-lg"
          initial={{ opacity: 0 }}
          animate={{
            opacity: 0.6,
            boxShadow: `0px 0px 20px ${glowColor}`
          }}
          exit={{ opacity: 0 }}
          style={{ backgroundColor: "transparent" }}
        />
      )}
    </motion.button>
  );
};

export default MagneticButton;
