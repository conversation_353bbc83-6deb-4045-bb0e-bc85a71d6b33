"use client";

import React, { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";

interface ParallaxSectionProps {
  children: React.ReactNode;
  className?: string;
  speed?: number;
  direction?: "up" | "down" | "left" | "right";
  offset?: number;
}

const ParallaxSection: React.FC<ParallaxSectionProps> = ({
  children,
  className = "",
  speed = 0.2,
  direction = "up",
  offset = 0,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  
  // Get scroll progress for this section
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });
  
  // Calculate transform values based on direction
  const getTransformValue = () => {
    switch (direction) {
      case "up":
        return useTransform(scrollYProgress, [0, 1], [`${offset}%`, `${-100 * speed + offset}%`]);
      case "down":
        return useTransform(scrollYProgress, [0, 1], [`${offset}%`, `${100 * speed + offset}%`]);
      case "left":
        return useTransform(scrollYProgress, [0, 1], [`${offset}%`, `${-100 * speed + offset}%`]);
      case "right":
        return useTransform(scrollYProgress, [0, 1], [`${offset}%`, `${100 * speed + offset}%`]);
      default:
        return useTransform(scrollYProgress, [0, 1], [`${offset}%`, `${-100 * speed + offset}%`]);
    }
  };
  
  // Get opacity based on scroll progress
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.6, 1, 1, 0.6]);
  
  // Get transform property name based on direction
  const getTransformProperty = () => {
    return direction === "up" || direction === "down" ? "y" : "x";
  };

  return (
    <div ref={ref} className={`relative overflow-hidden ${className}`}>
      <motion.div
        style={{
          [getTransformProperty()]: getTransformValue(),
          opacity,
        }}
        transition={{ type: "spring", stiffness: 100, damping: 30 }}
      >
        {children}
      </motion.div>
    </div>
  );
};

export default ParallaxSection;
