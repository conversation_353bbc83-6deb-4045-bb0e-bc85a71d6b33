import { motion } from "framer-motion";
const LoaderComponent = () => (
  <motion.div
    className="min-h-screen bg-gradient-to-br from-black to-green-900 flex flex-col items-center justify-center px-4 py-20 text-white"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ duration: 0.5 }}
  >
    <motion.div
      className="relative z-10 flex flex-col items-center"
      initial={{ scale: 0.9 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="w-20 h-20 mb-8 relative"
        animate={{
          rotate: 360,
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "linear",
        }}
      >
        <div className="absolute inset-0 rounded-full border-t-4 border-green-500 border-opacity-80"></div>
        <div className="absolute inset-0 rounded-full border-r-4 border-green-300 border-opacity-40"></div>
        <div className="absolute inset-0 rounded-full border-b-4 border-green-700 border-opacity-60"></div>
      </motion.div>

      <motion.h2
        className="text-2xl font-bold mb-4 text-green-400"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        Loading Domain Data
      </motion.h2>

      <motion.div
        className="bg-black bg-opacity-40 backdrop-blur-sm px-6 py-3 rounded-lg border border-green-500/30 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        <p className="text-gray-300">
          Please wait while we prepare your domain content...
        </p>
      </motion.div>
    </motion.div>
  </motion.div>
);

export default LoaderComponent;
