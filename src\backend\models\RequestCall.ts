import { Schema, model, models } from "mongoose";
import { Types } from "mongoose";

const RequestCall = new Schema(
  {
    name: {
      type: String,
      trim: true,
      required: true,
    },
    email: {
      type: String,
      trim: true,
      required: true,
    },
    phone: {
      type: String,
      trim: true,
      required: true,
    },
    details: {
      type: String,
      trim: true,
    },
    experience: {
      type: String,
      trim: true,
    },
    course: {
      type: String,
      trim: true,
    },
    type: {
      type: String,
      enum: ["call", "apply-course"],
      default: "call",
    },
    courseId: {
      type: Types.ObjectId,
      trim: true,
      ref: "courses",
    },
  },
  {
    timestamps: true,
  }
);

export default models.requestCall || model("requestCall", RequestCall);
