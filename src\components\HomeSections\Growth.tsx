import React, { useState, useEffect, useRef } from "react";
import { motion, useScroll, useTransform, useInView } from "framer-motion";

// SVG components for tree branches and leaves
interface BranchProps {
  isInView: boolean;
  index: number;
  color?: string;
}

// Desktop left branch
const LeftBranch: React.FC<BranchProps> = ({ isInView, index, color = "#1A803D" }) => (
  <motion.svg
    width="120"
    height="50"
    viewBox="0 0 120 50"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="absolute right-0 top-6 hidden md:block"
    style={{ right: "calc(50% + 15px)" }}
    initial={{ opacity: 0, pathLength: 0 }}
    animate={isInView ? { opacity: 1, pathLength: 1 } : { opacity: 0, pathLength: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
  >
    <motion.path
      d="M0 25H80C90 25 100 5 120 5"
      stroke={color}
      strokeWidth="3"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
      transition={{ duration: 0.8, delay: index * 0.1 }}
    />
    {/* Left-side leaves */}
    <motion.path
      d="M30 20C30 20 25 10 35 5"
      stroke={color}
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
    />
    <motion.path
      d="M50 20C50 20 45 5 55 8"
      stroke={color}
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 + 0.4 }}
    />
    <motion.ellipse
      cx="35"
      cy="5"
      rx="3"
      ry="4"
      fill={color}
      initial={{ scale: 0 }}
      animate={isInView
        ? {
            scale: [0, 1, 1.1, 1],
            rotate: [0, 5, -5, 0]
          }
        : { scale: 0 }
      }
      transition={{
        duration: 0.8,
        delay: index * 0.1 + 0.5,
        times: [0, 0.6, 0.8, 1]
      }}
      whileHover={{ scale: 1.2, rotate: 10 }}
    />
    <motion.ellipse
      cx="55"
      cy="8"
      rx="3"
      ry="4"
      fill={color}
      initial={{ scale: 0 }}
      animate={isInView
        ? {
            scale: [0, 1, 1.1, 1],
            rotate: [0, -5, 5, 0]
          }
        : { scale: 0 }
      }
      transition={{
        duration: 0.8,
        delay: index * 0.1 + 0.6,
        times: [0, 0.6, 0.8, 1]
      }}
      whileHover={{ scale: 1.2, rotate: -10 }}
    />
  </motion.svg>
);

// Desktop right branch
const RightBranch: React.FC<BranchProps> = ({ isInView, index, color = "#1A803D" }) => (
  <motion.svg
    width="120"
    height="50"
    viewBox="0 0 120 50"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="absolute left-0 top-6 hidden md:block"
    style={{ left: "calc(50% + 15px)" }}
    initial={{ opacity: 0, pathLength: 0 }}
    animate={isInView ? { opacity: 1, pathLength: 1 } : { opacity: 0, pathLength: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
  >
    <motion.path
      d="M120 25H40C30 25 20 5 0 5"
      stroke={color}
      strokeWidth="3"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
      transition={{ duration: 0.8, delay: index * 0.1 }}
    />
    {/* Right-side leaves */}
    <motion.path
      d="M70 20C70 20 75 10 65 5"
      stroke={color}
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
    />
    <motion.path
      d="M50 20C50 20 55 5 45 8"
      stroke={color}
      strokeWidth="1.5"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 + 0.4 }}
    />
    <motion.ellipse
      cx="65"
      cy="5"
      rx="3"
      ry="4"
      fill={color}
      initial={{ scale: 0 }}
      animate={isInView
        ? {
            scale: [0, 1, 1.1, 1],
            rotate: [0, 5, -5, 0]
          }
        : { scale: 0 }
      }
      transition={{
        duration: 0.8,
        delay: index * 0.1 + 0.5,
        times: [0, 0.6, 0.8, 1]
      }}
      whileHover={{ scale: 1.2, rotate: 10 }}
    />
    <motion.ellipse
      cx="45"
      cy="8"
      rx="3"
      ry="4"
      fill={color}
      initial={{ scale: 0 }}
      animate={isInView
        ? {
            scale: [0, 1, 1.1, 1],
            rotate: [0, -5, 5, 0]
          }
        : { scale: 0 }
      }
      transition={{
        duration: 0.8,
        delay: index * 0.1 + 0.6,
        times: [0, 0.6, 0.8, 1]
      }}
      whileHover={{ scale: 1.2, rotate: -10 }}
    />
  </motion.svg>
);

// Mobile branch with leaves (shown only on mobile/tablet)
const MobileBranch: React.FC<BranchProps> = ({ isInView, index, color = "#1A803D" }) => (
  <motion.svg
    width="60"
    height="40"
    viewBox="0 0 60 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="absolute left-3 sm:left-4 top-6 block md:hidden"
    style={{ marginLeft: "15px" }}
    initial={{ opacity: 0 }}
    animate={isInView ? { opacity: 1 } : { opacity: 0 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
  >
    {/* Horizontal branch */}
    <motion.path
      d="M0 20H45"
      stroke={color}
      strokeWidth="3"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    />
    {/* Leaves */}
    <motion.path
      d="M20 20C20 20 15 5 25 5"
      stroke={color}
      strokeWidth="2"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
    />
    <motion.ellipse
      cx="25"
      cy="5"
      rx="4"
      ry="5"
      fill={color}
      initial={{ scale: 0 }}
      animate={isInView
        ? {
            scale: [0, 1, 1.1, 1],
            rotate: [0, 5, -5, 0]
          }
        : { scale: 0 }
      }
      transition={{
        duration: 0.8,
        delay: index * 0.1 + 0.4,
        times: [0, 0.6, 0.8, 1]
      }}
      whileHover={{ scale: 1.2, rotate: 10 }}
    />

    {/* Add a subtle animation to the branch */}
    <motion.path
      d="M35 20C35 20 40 10 35 5"
      stroke={color}
      strokeWidth="2"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 + 0.5 }}
    />

    {/* Additional leaf */}
    <motion.ellipse
      cx="35"
      cy="5"
      rx="4"
      ry="5"
      fill={color}
      initial={{ scale: 0 }}
      animate={isInView
        ? {
            scale: [0, 1, 1.1, 1],
            rotate: [0, -5, 5, 0]
          }
        : { scale: 0 }
      }
      transition={{
        duration: 0.8,
        delay: index * 0.1 + 0.6,
        times: [0, 0.6, 0.8, 1]
      }}
      whileHover={{ scale: 1.2, rotate: -10 }}
    />
  </motion.svg>
);

// Tree trunk component for the main vertical timeline
const TreeTrunk: React.FC<{progress: any, isMobile: boolean, isTablet: boolean}> = ({ progress, isMobile, isTablet }) => (
  <>
    {/* Base tree trunk (static) */}
    <svg
      className={`absolute ${isMobile ? 'left-3' : isTablet ? 'left-4' : 'left-0 md:left-1/2'} top-0 bottom-0 h-full transform md:translate-x-[-50%] ${isMobile ? 'ml-0' : 'ml-6 md:ml-0'} z-0`}
      width={isMobile ? "20" : isTablet ? "24" : "30"}
      viewBox="0 0 60 1000"
      preserveAspectRatio="none"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Tree trunk with varying width to look more natural */}
      <motion.path
        d="M30 0C35 50 25 100 30 150C40 200 20 250 30 300C35 350 25 400 30 450C40 500 20 550 30 600C35 650 25 700 30 750C40 800 20 850 30 900C35 950 30 1000 30 1000"
        stroke="url(#treeGradient)"
        strokeWidth={isMobile ? "16" : isTablet ? "20" : "24"}
        strokeLinecap="round"
        fill="none"
        animate={{
          d: [
            "M30 0C35 50 25 100 30 150C40 200 20 250 30 300C35 350 25 400 30 450C40 500 20 550 30 600C35 650 25 700 30 750C40 800 20 850 30 900C35 950 30 1000 30 1000",
            "M30 0C32 50 28 100 30 150C38 200 22 250 30 300C32 350 28 400 30 450C38 500 22 550 30 600C32 650 28 700 30 750C38 800 22 850 30 900C32 950 30 1000 30 1000",
            "M30 0C35 50 25 100 30 150C40 200 20 250 30 300C35 350 25 400 30 450C40 500 20 550 30 600C35 650 25 700 30 750C40 800 20 850 30 900C35 950 30 1000 30 1000"
          ],
          transition: {
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }
        }}
      />

      {/* Tree bark texture */}
      <motion.path
        d="M20 100C25 120 35 130 30 150M22 300C27 320 33 330 28 350M22 500C27 520 33 530 28 550M22 700C27 720 33 730 28 750M22 900C27 920 33 930 28 950"
        stroke="#0F5D28"
        strokeWidth="2"
        strokeLinecap="round"
        opacity="0.6"
        animate={{
          opacity: [0.4, 0.6, 0.4],
          transition: {
            duration: 10,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }
        }}
      />

      <defs>
        <linearGradient id="treeGradient" x1="0" y1="0" x2="0" y2="1000" gradientUnits="userSpaceOnUse">
          <stop offset="0%" stopColor="#1A803D" />
          <stop offset="50%" stopColor="#2DA84F" />
          <stop offset="100%" stopColor="#8FD9A8" />
        </linearGradient>
      </defs>
    </svg>

    {/* Animated progress overlay */}
    <motion.svg
      className={`absolute ${isMobile ? 'left-3' : isTablet ? 'left-4' : 'left-0 md:left-1/2'} top-0 bottom-0 h-full transform md:translate-x-[-50%] ${isMobile ? 'ml-0' : 'ml-6 md:ml-0'} z-[1]`}
      width={isMobile ? "20" : isTablet ? "24" : "30"}
      viewBox="0 0 60 1000"
      preserveAspectRatio="none"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{
        clipPath: `inset(0px 0px ${100 - progress * 100}% 0px)`
      }}
    >
      <path
        d="M30 0C35 50 25 100 30 150C40 200 20 250 30 300C35 350 25 400 30 450C40 500 20 550 30 600C35 650 25 700 30 750C40 800 20 850 30 900C35 950 30 1000 30 1000"
        stroke="url(#treeGradientAnimated)"
        strokeWidth={isMobile ? "16" : isTablet ? "20" : "24"}
        strokeLinecap="round"
        fill="none"
      />

      {/* Animated tree bark texture */}
      <path
        d="M20 100C25 120 35 130 30 150M22 300C27 320 33 330 28 350M22 500C27 520 33 530 28 550M22 700C27 720 33 730 28 750M22 900C27 920 33 930 28 950"
        stroke="#0F5D28"
        strokeWidth="2"
        strokeLinecap="round"
        opacity="0.7"
      />

      <defs>
        <linearGradient id="treeGradientAnimated" x1="0" y1="0" x2="0" y2="1000" gradientUnits="userSpaceOnUse">
          <stop offset="0%" stopColor="#1A803D" />
          <stop offset="50%" stopColor="#2DA84F" />
          <stop offset="100%" stopColor="#8FD9A8" />
        </linearGradient>
      </defs>

      {/* Small branches and leaves along the trunk */}
      {/* Branch 1 - Right side */}
      <path d="M40 200C40 200 50 190 55 195" stroke="#1A803D" strokeWidth="3" strokeLinecap="round" />
      <path d="M55 195C55 195 60 190 57 185" stroke="#1A803D" strokeWidth="2" strokeLinecap="round" />
      <ellipse cx="57" cy="185" rx="4" ry="5" transform="rotate(-15 57 185)" fill="#1A803D" />

      {/* Branch 2 - Left side */}
      <path d="M20 350C20 350 10 340 5 345" stroke="#1A803D" strokeWidth="3" strokeLinecap="round" />
      <path d="M5 345C5 345 0 340 3 335" stroke="#1A803D" strokeWidth="2" strokeLinecap="round" />
      <ellipse cx="3" cy="335" rx="4" ry="5" transform="rotate(15 3 335)" fill="#1A803D" />

      {/* Branch 3 - Right side */}
      <path d="M40 500C40 500 50 490 55 495" stroke="#1A803D" strokeWidth="3" strokeLinecap="round" />
      <path d="M55 495C55 495 60 490 57 485" stroke="#1A803D" strokeWidth="2" strokeLinecap="round" />
      <ellipse cx="57" cy="485" rx="4" ry="5" transform="rotate(-15 57 485)" fill="#1A803D" />

      {/* Branch 4 - Left side */}
      <path d="M20 650C20 650 10 640 5 645" stroke="#1A803D" strokeWidth="3" strokeLinecap="round" />
      <path d="M5 645C5 645 0 640 3 635" stroke="#1A803D" strokeWidth="2" strokeLinecap="round" />
      <ellipse cx="3" cy="635" rx="4" ry="5" transform="rotate(15 3 635)" fill="#1A803D" />

      {/* Branch 5 - Right side */}
      <path d="M40 800C40 800 50 790 55 795" stroke="#1A803D" strokeWidth="3" strokeLinecap="round" />
      <path d="M55 795C55 795 60 790 57 785" stroke="#1A803D" strokeWidth="2" strokeLinecap="round" />
      <ellipse cx="57" cy="785" rx="4" ry="5" transform="rotate(-15 57 785)" fill="#1A803D" />

      {/* Additional smaller branches for more tree-like appearance */}
      <path d="M35 250C35 250 45 245 42 240" stroke="#1A803D" strokeWidth="2" strokeLinecap="round" />
      <ellipse cx="42" cy="240" rx="3" ry="4" transform="rotate(-10 42 240)" fill="#1A803D" />

      <path d="M25 400C25 400 15 395 18 390" stroke="#1A803D" strokeWidth="2" strokeLinecap="round" />
      <ellipse cx="18" cy="390" rx="3" ry="4" transform="rotate(10 18 390)" fill="#1A803D" />

      <path d="M35 550C35 550 45 545 42 540" stroke="#1A803D" strokeWidth="2" strokeLinecap="round" />
      <ellipse cx="42" cy="540" rx="3" ry="4" transform="rotate(-10 42 540)" fill="#1A803D" />

      <path d="M25 700C25 700 15 695 18 690" stroke="#1A803D" strokeWidth="2" strokeLinecap="round" />
      <ellipse cx="18" cy="690" rx="3" ry="4" transform="rotate(10 18 690)" fill="#1A803D" />

      <path d="M35 850C35 850 45 845 42 840" stroke="#1A803D" strokeWidth="2" strokeLinecap="round" />
      <ellipse cx="42" cy="840" rx="3" ry="4" transform="rotate(-10 42 840)" fill="#1A803D" />
    </motion.svg>
  </>
);
// Custom hook to detect scroll direction
const useScrollDirection = () => {
  const [scrollDirection, setScrollDirection] = useState<"up" | "down">("down");
  const prevScrollY = useRef(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      if (currentScrollY > prevScrollY.current) {
        setScrollDirection("down");
      } else {
        setScrollDirection("up");
      }
      prevScrollY.current = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return scrollDirection;
};

// Define features outside the component to avoid React hooks dependency issues
const features = [
  {
    icon: "💡",
    title: "Flexible Learning Modes to Accelerate Your Career",
    description:
      "Choose between 100% Live Online Classes or Hybrid Classes, offering the flexibility to learn remotely while benefiting from structured in-person interactions.",
    color: "#000", // green-400
  },
  {
    icon: "📈",
    title: "Specialise in Your Chosen Domain",
    description:
      "Tailor your learning experience by selecting from specialised tracks in Technology, Management, or other relevant fields to gain focused and industry-relevant knowledge.",
    color: "#000", // teal-400
  },
  {
    icon: "🏭",
    title: "Hands-On Experience with Real Projects",
    description:
      "Gain practical experience through real-world projects, guided by industry experts, and enhance your skills for tangible, real-life challenges. Receive a Co-Branded Project Certification upon completion.",
    color: "#000", // blue-400
  },
  {
    icon: "🧑🏻‍💻",
    title: "Achieve Your Career Objectives",
    description:
      "Accelerate your career progression with comprehensive learning support, including advanced training, mock interviews, resume building, and direct interview opportunities.",
    color: "#000", // purple-400
  },
];

const GrowthSection = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const { scrollYProgress } = useScroll();
  const lineHeight = useTransform(scrollYProgress, [0, 0.8], ["0%", "100%"]);
  const scrollDirection = useScrollDirection();

  // Create refs for each timeline item outside the render function
  const itemRefs = features.map(() => useRef(null));
  const itemInViewArray = features.map((_, i) => useInView(itemRefs[i], { amount: 0.3, once: false }));

  // Handle responsive layout detection
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
      setIsTablet(window.innerWidth >= 640 && window.innerWidth < 768);
    };

    // Set initial values
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <section className="py-16 bg-gradient-to-br from-green-50 to-green-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 sm:mb-4 text-[#1A803D] px-2">
            Why settle for less when Grow Grid takes you further?
          </h2>
          <h3 className="text-lg sm:text-xl md:text-2xl text-gray-700 font-semibold px-2">
            Training: Your First Priority for Career Growth
          </h3>
        </div>

        <div className="relative max-w-5xl mx-auto px-2 sm:px-4">
          {/* Tree trunk timeline */}
          <TreeTrunk progress={lineHeight} isMobile={isMobile} isTablet={isTablet} />

          {/* Timeline items */}
          <div className="relative">
            {features.map((feature, index) => {
              const isInView = itemInViewArray[index];

              return (
                <motion.div
                  ref={itemRefs[index]}
                  key={index}
                  className={`flex flex-col md:flex-row mb-12 sm:mb-16 relative ${
                    index % 2 === 0 ? "md:flex-row-reverse" : ""
                  }`}
                  onMouseEnter={() => setActiveIndex(index)}
                  onMouseLeave={() => setActiveIndex(null)}
                  onTouchStart={() => setActiveIndex(index)}
                  onTouchEnd={() => setTimeout(() => setActiveIndex(null), 1000)}
                  initial={{ opacity: 0, y: 50 }}
                  animate={isInView
                    ? { opacity: 1, y: 0 }
                    : { opacity: 0, y: scrollDirection === "down" ? 50 : -50 }
                  }
                  transition={{
                    type: "spring",
                    stiffness: 100,
                    damping: 15,
                    delay: index * 0.1
                  }}
                >
                  {/* Tree branches with leaves */}
                  {/* Desktop branches */}
                  {index % 2 === 0 ? (
                    <LeftBranch isInView={isInView} index={index} color="#1A803D" />
                  ) : (
                    <RightBranch isInView={isInView} index={index} color="#1A803D" />
                  )}

                  {/* Mobile branch */}
                  <MobileBranch isInView={isInView} index={index} color="#1A803D" />

                  {/* Timeline dot with pulse effect */}
                  <div className={`absolute ${isMobile ? 'left-3' : isTablet ? 'left-4' : 'left-6 md:left-1/2'} transform md:translate-x-[-50%] mt-6 z-10`} style={{ marginLeft: isMobile ? "10px" : isTablet ? "12px" : "15px" }}>
                    {/* Outer pulse animation */}
                    <motion.div
                      className="absolute w-6 h-6 sm:w-8 sm:h-8 rounded-full opacity-30"
                      style={{ backgroundColor: feature.color }}
                      initial={{ scale: 0 }}
                      animate={isInView
                        ? {
                            scale: activeIndex === index ? [1, 1.5, 1] : 1,
                            opacity: activeIndex === index ? [0.3, 0.1, 0.3] : 0.3
                          }
                        : { scale: 0, opacity: 0 }
                      }
                      transition={{
                        duration: 2,
                        repeat: isInView && activeIndex === index ? Infinity : 0,
                        repeatType: "loop"
                      }}
                    ></motion.div>

                    {/* Main dot */}
                    <motion.div
                      className="relative w-4 h-4 sm:w-6 sm:h-6 rounded-full flex items-center justify-center"
                      style={{ backgroundColor: feature.color }}
                      initial={{ scale: 0 }}
                      animate={isInView ? { scale: 1 } : { scale: 0 }}
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 1.2 }}
                      transition={{
                        type: "spring",
                        stiffness: 300,
                        damping: 10,
                        delay: index * 0.1
                      }}
                    >
                      {/* Inner white dot */}
                      <motion.div
                        className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-white rounded-full"
                        initial={{ scale: 0 }}
                        animate={isInView ? { scale: 1 } : { scale: 0 }}
                        transition={{ delay: index * 0.1 + 0.2 }}
                      ></motion.div>
                    </motion.div>
                  </div>

                  {/* Content */}
                  <motion.div
                    className={`ml-10 sm:ml-16 md:ml-0 md:w-[calc(50%-60px)] ${
                      index % 2 === 0 ? "md:mr-auto md:pl-0 md:pr-6 lg:pr-10" : "md:ml-auto md:pl-6 lg:pl-10 md:pr-0"
                    }`}
                    initial={{ opacity: 0, x: index % 2 === 0 ? 50 : -50 }}
                    animate={isInView
                      ? { opacity: 1, x: 0 }
                      : {
                          opacity: 0,
                          x: index % 2 === 0
                            ? (scrollDirection === "down" ? 50 : -50)
                            : (scrollDirection === "down" ? -50 : 50)
                        }
                    }
                    transition={{
                      type: "spring",
                      stiffness: 100,
                      damping: 15,
                      delay: index * 0.1 + 0.2
                    }}
                  >
                    <motion.div
                      className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-4 sm:p-6 border border-gray-100"
                      whileHover={{
                        scale: 1.03,
                        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                        borderColor: feature.color
                      }}
                      whileTap={{
                        scale: 1.02,
                        borderColor: feature.color
                      }}
                      transition={{ type: "spring", stiffness: 400, damping: 17 }}
                    >
                      <div className="flex items-start">
                        <motion.div
                          className="text-2xl sm:text-3xl md:text-4xl mr-3 sm:mr-4 p-1.5 sm:p-2 rounded-full flex-shrink-0"
                          whileHover={{
                            rotate: [0, -10, 10, -10, 0],
                            scale: 1.1,
                            backgroundColor: `${"#1A803D"}40`
                          }}
                          whileTap={{
                            rotate: [0, -5, 5, 0],
                            scale: 1.1,
                            backgroundColor: `#1A803D`
                          }}
                          transition={{ duration: 0.5 }}
                        >
                          {feature.icon}
                        </motion.div>
                        <div>
                          <motion.h4
                            className="font-bold text-base sm:text-lg mb-1 sm:mb-2 bg-clip-text text-transparent"
                            style={{ backgroundImage: `linear-gradient(90deg, #1A803D)` }}
                          >
                            {index + 1}. {feature.title}
                          </motion.h4>
                          <p className="text-sm sm:text-base text-gray-600">{feature.description}</p>
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                </motion.div>
              );
            })}
          </div>
        </div>

        <div className="mt-8 sm:mt-12 text-center">
          <motion.button
            className="bg-[#1A803D] text-white font-bold py-2 px-6 sm:py-3 sm:px-8 rounded-full shadow-lg"
            whileHover={{
              scale: 1.05,
              boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
            }}
            whileTap={{ scale: 0.98 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <span className="text-sm sm:text-base">Start Your Growth Journey</span>
          </motion.button>
        </div>
      </div>
    </section>
  );
};

export default GrowthSection;
