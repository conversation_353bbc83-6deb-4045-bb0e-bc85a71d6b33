import axios from "axios";

export const getCourseAll = async () => {
  const response = await axios.get(`/api/course`);
  return response.data;
};

export const getCourseBySlug = async (slug: string) => {
  const response = await axios.get(`/api/course/${slug}`);
  return response.data;
};

export const getCourseMinimal = async () => {
  const response = await axios.get(`/api/course/minimal`);
  return response.data;
}
