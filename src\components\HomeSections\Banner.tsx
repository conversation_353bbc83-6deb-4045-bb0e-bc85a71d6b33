"use client";
import React, { useState, useEffect, useRef } from "react";
import Modal from "react-modal";
import Image from "next/image";
import { X, Play, CheckCircle } from "lucide-react";
import {
  motion,
  useScroll,
  useTransform,
  AnimatePresence,
} from "framer-motion";
import { staggeredList, staggeredItem } from "@/utils/advancedAnimations";
import { createRequest } from "@/service/request-call";

// Ensure Modal is accessible
// Modal.setAppElement("#root"); // Change this to match your root element ID

const animatedTexts = ["Training", "Skillset", "Internships"];
const benefits = [
  "Industry-relevant curriculum",
  "Expert instructors",
  "Placement support",
  "Live project experience",
];

// Array of image paths for the carousel
const carouselImages = [
  "/assets/images/aiImage1.jpeg",
  "/assets/images/aiImage3.jpeg",
  "/assets/images/aiImage2.jpeg",
  "/assets/images/aiImage4.jpeg",
];

export default function EnhancedBanner() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    email: "",
    experience: "",
    course: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [activeTextIndex, setActiveTextIndex] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  // Add new state for typewriter effect
  const [displayText, setDisplayText] = useState("");
  const [isTyping, setIsTyping] = useState(true);

  // Modified text rotation effect with typewriter animation
  useEffect(() => {
    if (isHovering) return;

    let typingTimer: NodeJS.Timeout;
    let currentIndex = 0;
    let currentTextIndex = activeTextIndex;
    let currentText = animatedTexts[currentTextIndex];
    let isErasing = false;

    const typeNextChar = () => {
      if (isErasing) {
        // Erasing text
        if (currentIndex > 0) {
          currentIndex--;
          setDisplayText(currentText.substring(0, currentIndex));
          typingTimer = setTimeout(typeNextChar, 50); // Faster when erasing
        } else {
          // Move to next text
          isErasing = false;
          currentTextIndex = (currentTextIndex + 1) % animatedTexts.length;
          currentText = animatedTexts[currentTextIndex];
          setActiveTextIndex(currentTextIndex);
          typingTimer = setTimeout(typeNextChar, 500); // Pause before typing next word
        }
      } else {
        // Typing text
        if (currentIndex < currentText.length) {
          currentIndex++;
          setDisplayText(currentText.substring(0, currentIndex));
          typingTimer = setTimeout(typeNextChar, 150); // Slower when typing
        } else {
          // Pause at the end of the word before erasing
          isErasing = true;
          typingTimer = setTimeout(typeNextChar, 1500);
        }
      }
    };

    // Start the typing animation
    typingTimer = setTimeout(typeNextChar, 500);

    return () => clearTimeout(typingTimer);
  }, [isHovering]);

  // Image carousel effect
  useEffect(() => {
    const imageInterval = setInterval(() => {
      setActiveImageIndex(
        (prevIndex) => (prevIndex + 1) % carouselImages.length
      );
    }, 2500); // Change image every 2.5 seconds

    return () => clearInterval(imageInterval);
  }, []);

  const openModal = () => {
    try {
      setIsModalOpen(true);
    } catch (error) {
      console.error("Error opening modal:", error);
    }
  };
  const closeModal = () => {
    setIsModalOpen(false);
    // Reset form submission state when closing modal
    if (isSubmitted) {
      setIsSubmitted(false);
      setFormData({
        name: "",
        phone: "",
        email: "",
        experience: "",
        course: "",
      });
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.ChangeEvent<HTMLFormElement>) => {
    try {
      e.preventDefault();
      setIsSubmitting(true);

      const response = createRequest(formData);

      setIsModalOpen(false);
      setIsSubmitting(false);
      setIsSubmitted(false);
      setFormData({
        name: "",
        phone: "",
        email: "",
        experience: "",
        course: "",
      });
    } catch (error) {
      setIsSubmitting(false);
    }
  };

  // Custom modal styles
  const customStyles = {
    overlay: {
      backgroundColor: "rgba(0, 0, 0, 0.75)",
      zIndex: 1000,
    },
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      padding: 0,
      borderRadius: "12px",
      border: "none",
      boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
      maxWidth: "500px",
      width: "90%",
    },
  };

  const bannerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: bannerRef,
    offset: ["start end", "end start"],
  });

  // Parallax effect for background
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "20%"]);

  return (
    <motion.div
      ref={bannerRef}
      className="w-full pt-16 pb-12 overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container mx-auto px-4 md:px-8 lg:px-16 flex flex-col lg:flex-row items-center justify-between gap-12">
        {/* Left Section - Text Content */}
        <motion.div
          className="lg:w-1/2 text-center lg:text-left space-y-8 max-w-xl"
          variants={staggeredList}
          initial="hidden"
          animate="visible"
        >
          {/* Animated Headline */}
          <motion.div className="font-bold" variants={staggeredItem}>
            <span className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight inline-block mr-2">
              Need
            </span>

            <motion.span
              className="text-green-800 py-2 rounded-sm"
              onMouseEnter={() => setIsHovering(true)}
              onMouseLeave={() => setIsHovering(false)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20, rotateX: -20 }}
              animate={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 15,
                delay: 0.3,
              }}
            >
              {/* Replace AnimatePresence with typewriter text */}
              {/* <div className="absolute inset-0 flex items-center justify-center"> */}
              <span className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight inline-block">
                {displayText}
                <span className="animate-pulse font-medium">|</span>
              </span>
              {/* </div> */}
            </motion.span>

            <span className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight inline-block">
              We've got your back!
            </span>
          </motion.div>

          <motion.h2
            className="text-xl md:text-2xl text-gray-700 font-semibold"
            variants={staggeredItem}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            Start your learning journey with zero hassles
          </motion.h2>

          {/* Benefits */}
          <motion.div
            className="space-y-3"
            variants={staggeredList}
            initial="hidden"
            animate="visible"
            transition={{ delayChildren: 0.8, staggerChildren: 0.1 }}
          >
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                className="flex items-center gap-2 text-gray-700"
                variants={staggeredItem}
                custom={index}
                whileHover={{ x: 5 }}
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.9 + index * 0.1, type: "spring" }}
                >
                  <CheckCircle className="text-green-600 w-5 h-5" />
                </motion.div>
                <span>{benefit}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Section */}
          <motion.div
            className="flex flex-col sm:flex-row items-center gap-4"
            variants={staggeredItem}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            {/* <motion.button
              onClick={() => (window.location.href = "/courses")}
              className="px-6 py-3 bg-green-700 text-white font-semibold rounded-lg w-full sm:w-auto text-center"
              whileHover={{ scale: 1.05, backgroundColor: "#15803d" }}
              whileTap={{ scale: 0.95 }}
            >
              Explore Courses
            </motion.button> */}

            <motion.button
              onClick={openModal}
              className="px-6 py-3 border-2 border-green-700 text-green-700 font-semibold rounded-lg w-full sm:w-auto text-center"
              whileHover={{ scale: 1.05, backgroundColor: "#f0fdf4" }}
              whileTap={{ scale: 0.95 }}
            >
              Request Call Back
            </motion.button>
          </motion.div>

          {/* Video Link */}
          <motion.div
            className="flex items-center justify-center lg:justify-start gap-3 text-green-700 cursor-pointer"
            variants={staggeredItem}
            whileHover={{ scale: 1.05, color: "#166534" }}
          >
            <motion.div
              className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center"
              whileHover={{ scale: 1.1, backgroundColor: "#dcfce7" }}
              whileTap={{ scale: 0.95 }}
              animate={{
                boxShadow: [
                  "0px 0px 0px rgba(22, 101, 52, 0)",
                  "0px 0px 20px rgba(22, 101, 52, 0.5)",
                  "0px 0px 0px rgba(22, 101, 52, 0)",
                ],
              }}
              transition={{
                boxShadow: { repeat: Infinity, duration: 2 },
                type: "spring",
              }}
            >
              <Play className="w-5 h-5 ml-1" />
            </motion.div>
            <span className="font-medium">See how it works</span>
          </motion.div>
        </motion.div>

        {/* Right Section - Image with Animation */}
        <motion.div
          className="lg:w-1/2 relative h-[300px] md:h-[400px] lg:h-[500px] w-full"
          initial={{ opacity: 0, x: 100 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
        >
          <motion.div
            className="absolute -z-10 w-full h-full rounded-full bg-green-100 blur-3xl opacity-30"
            style={{ y: backgroundY }}
            animate={{
              scale: [1, 1.05, 1],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              repeatType: "reverse",
            }}
          />

          <div className="h-full w-full rounded-lg shadow-xl relative">
            <div className="relative z-10 h-full w-full overflow-hidden rounded-lg">
              {carouselImages.map((src, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: index === activeImageIndex ? 1 : 0,
                    scale: index === activeImageIndex ? 1 : 0.9,
                  }}
                  transition={{ duration: 0.5 }}
                  className="absolute inset-0 w-full h-full"
                >
                  <Image
                    src={src}
                    alt={`Learning Banner ${index + 1}`}
                    fill
                    priority={index === 0}
                    sizes="(max-width: 768px) 100vw, 50vw"
                    className="rounded-lg shadow-2xl object-cover"
                    style={{ objectFit: "cover", objectPosition: "center" }}
                  />
                </motion.div>
              ))}

              {/* Floating Elements */}
              <motion.div
                className="absolute top-0 right-0 bg-white p-4 rounded-bl-lg rounded-tr-lg shadow-lg z-20"
                initial={{ opacity: 0, y: 20 }}
                animate={{
                  opacity: 1,
                  y: [0, -10, 0],
                }}
                transition={{
                  y: { repeat: Infinity, duration: 2, ease: "easeInOut" },
                  opacity: { delay: 0.5 },
                }}
                whileHover={{ scale: 1.1, rotate: 5 }}
              >
                <div className="text-green-700 font-bold">100+</div>
                <div className="text-xs text-gray-600">Expert Mentors</div>
              </motion.div>

              <motion.div
                className="absolute bottom-0 left-0 bg-white p-4 rounded-bl-lg rounded-tr-lg shadow-lg z-20"
                initial={{ opacity: 0, y: -20 }}
                animate={{
                  opacity: 1,
                  y: [0, 10, 0],
                }}
                transition={{
                  y: { repeat: Infinity, duration: 2.5, ease: "easeInOut" },
                  opacity: { delay: 0.7 },
                }}
                whileHover={{ scale: 1.1, rotate: -5 }}
              >
                <div className="text-green-700 font-bold">92%</div>
                <div className="text-xs text-gray-600">Placement Support</div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Enhanced Modal for Request Call Back */}
      <Modal
        isOpen={isModalOpen}
        onRequestClose={closeModal}
        style={customStyles}
        contentLabel="Request Call Back Modal"
      >
        <div className="relative rounded-lg overflow-hidden">
          {/* Modal Close Button */}
          <button
            onClick={closeModal}
            className="absolute right-4 top-4 text-gray-500 hover:text-gray-700 z-10"
          >
            <X className="w-6 h-6" />
          </button>

          {/* Modal Header */}
          <div className="bg-green-700 text-white p-6">
            <h3 className="text-xl font-bold text-center">
              REQUEST A CALLBACK
            </h3>
            <p className="text-center text-green-100 mt-2">
              Our education expert will get in touch within 24 hours
            </p>
          </div>

          {isSubmitted ? (
            /* Success Message */
            <div className="p-8 bg-white flex flex-col items-center justify-center gap-4">
              <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-green-700" />
              </div>
              <h3 className="text-xl font-bold text-center text-gray-800">
                Thank You!
              </h3>
              <p className="text-center text-gray-600">
                Your request has been submitted successfully. Our team will
                contact you soon.
              </p>
              <button
                onClick={closeModal}
                className="mt-4 px-6 py-2 bg-green-700 text-white rounded-lg hover:bg-green-800 transition duration-300"
              >
                Close
              </button>
            </div>
          ) : (
            /* Form */
            <div className="p-6 bg-white">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="block w-full px-4 py-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-300"
                    required
                    placeholder="Your full name"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="phone"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="block w-full px-4 py-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-300"
                      required
                      placeholder="Your phone number"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="block w-full px-4 py-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-300"
                      required
                      placeholder="Your email address"
                    />
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="experience"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Work Experience
                  </label>
                  <select
                    id="experience"
                    name="experience"
                    value={formData.experience}
                    onChange={handleInputChange}
                    className="block w-full px-4 py-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-300"
                    required
                  >
                    <option value="">Select your experience</option>
                    <option value="0-1">0-1 years</option>
                    <option value="1-3">1-3 years</option>
                    <option value="3-5">3-5 years</option>
                    <option value="5+">5+ years</option>
                  </select>
                </div>

                <div>
                  <label
                    htmlFor="course"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Course Preference
                  </label>
                  <select
                    id="course"
                    name="course"
                    value={formData.course}
                    onChange={handleInputChange}
                    className="block w-full px-4 py-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-300"
                    required
                  >
                    <option value="">Select a course</option>
                    <option value="webdev">Web Development</option>
                    <option value="datascience">Data Science</option>
                    <option value="ai">Artificial Intelligence</option>
                    <option value="cybersecurity">Cyber Security</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div className="pt-4">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`w-full flex justify-center items-center py-3 px-4 rounded-md text-white font-medium ${
                      isSubmitting
                        ? "bg-green-600 cursor-not-allowed"
                        : "bg-green-700 hover:bg-green-800 shadow-md hover:shadow-lg transition transform hover:-translate-y-1"
                    }`}
                  >
                    {isSubmitting ? (
                      <>
                        <svg
                          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      "Request Call Back"
                    )}
                  </button>
                </div>

                <p className="text-xs text-center text-gray-500 mt-4">
                  By submitting this form, you agree to our privacy policy and
                  terms of service.
                </p>
              </form>
            </div>
          )}
        </div>
      </Modal>
    </motion.div>
  );
}
