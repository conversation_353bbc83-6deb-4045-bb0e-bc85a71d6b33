"use client";
import Image from "next/image";
import Header from "../Common/Header";
import { motion } from "framer-motion";
import { staggerContainer } from "@/utils/animations";

export default function Stats() {
  const statsItems = [
    {
      icon: "/assets/images/home/<USER>",
      title: "Hours of Learning",
      value: "30-35 hrs ⏰",
    },
    {
      icon: "/assets/images/home/<USER>",
      title: "Recorded Sessions",
      value: "Live, Interactive 🔥",
    },
    {
      icon: "/assets/images/home/<USER>",
      title: "Training Mode",
      value: "Hybrid 🚀",
    },
  ];

  return (
    <>
      <Header
        boldTitle="Accelerate your career"
        title="with our programs"
        subtitle="trusted by thousands to launch successful tech careers."
      />
      <div className="text-white py-6 px-3 sm:py-10 md:py-16 relative flex justify-center overflow-hidden">
        <Image
          src={"/assets/images/bgDesignRect1.png"}
          alt="Background"
          fill
          priority
          className="absolute top-0 left-0 right-0 bottom-0 z-0 object-cover"
          sizes="100vw"
        />
        <motion.div
          className="max-w-6xl w-full z-10 relative"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={staggerContainer}
        >
          <div className="flex flex-col sm:flex-row justify-between items-center text-center gap-4 sm:gap-6 md:gap-8">
            {statsItems.map((item, index) => (
              <motion.div
                key={index}
                className={`w-full sm:w-1/3 p-3 sm:p-4 md:p-6 backdrop-blur-sm bg-black/10 rounded-lg sm:rounded-xl ${
                  index === 1 ? "sm:border-l sm:border-r border-white/30" : ""
                }`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.5,
                  delay: index * 0.2,
                  ease: "easeOut",
                }}
                viewport={{ once: true }}
                whileHover={{
                  scale: 1.05,
                  backgroundColor: "rgba(0, 0, 0, 0.2)",
                  boxShadow:
                    "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                }}
              >
                <motion.div
                  className="text-4xl sm:text-5xl md:text-6xl mb-2 sm:mb-4"
                  whileHover={{ scale: 1.2, rotate: [0, -5, 5, 0] }}
                  transition={{ duration: 0.5 }}
                >
                  <Image
                    src={item.icon}
                    alt={item.title}
                    width={200}
                    height={200}
                    className="w-10 sm:w-12 md:w-14 mx-auto"
                  />
                </motion.div>
                <h2 className="text-lg sm:text-xl md:text-2xl font-extrabold mb-1 sm:mb-2">
                  {item.title}
                </h2>
                <motion.p
                  className="text-xs sm:text-sm bg-black/30 rounded-full px-3 sm:px-4 py-1 sm:py-2 mx-auto w-fit mt-1 sm:mt-2 font-medium"
                  whileHover={{
                    scale: 1.05,
                    backgroundColor: "rgba(0, 0, 0, 0.4)",
                  }}
                >
                  {item.value}
                </motion.p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </>
  );
}
