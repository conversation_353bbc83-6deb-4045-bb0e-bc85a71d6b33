import { motion, AnimatePresence } from "framer-motion";
import {
  FaCalendarAlt,
  FaLaptop,
  FaCheckCircle,
  FaProjectDiagram,
  FaClipboardCheck,
  FaClipboardList,
} from "react-icons/fa";
import ScrollReveal from "@/components/Common/ScrollReveal";

interface CurriculumContentProps {
  course: any;
  activePhase: number;
  setActivePhase: (phase: number) => void;
}

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1 },
  },
};

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
  },
};

const CurriculumContent = ({
  course,
  activePhase,
  setActivePhase,
}: CurriculumContentProps) => (
  <motion.section
    className="max-w-6xl mx-auto px-6 py-16"
    initial={{ opacity: 0 }}
    whileInView={{ opacity: 1 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true, amount: 0.1 }}
  >
    <ScrollReveal>
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-700 to-black">
          Program Curriculum
        </h2>
        <div className="h-1 w-24 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-6"></div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Our comprehensive curriculum is designed to build your skills
          progressively from fundamentals to advanced techniques.
        </p>
      </div>
    </ScrollReveal>

    {/* Interactive Timeline */}
    <div className="relative mb-20">
      {/* Timeline Track */}
      {/* <motion.div
          className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-green-700 to-green-400"
          initial={{ height: 0 }}
          whileInView={{ height: "100%" }}
          transition={{ duration: 1 }}
          viewport={{ once: true }}
        ></motion.div> */}

      {/* Phase Buttons */}
      <div className="flex justify-center mb-12 relative">
        <div className="flex flex-wrap justify-center gap-2 sm:gap-3 md:gap-4">
          {course?.phases.map((_: any, idx: any) => (
            <motion.button
              key={idx}
              onClick={() => setActivePhase(idx)}
              className={`relative z-10 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full border-2 transition-all duration-300 text-sm sm:text-base ${
                activePhase === idx
                  ? "bg-green-700 text-white border-green-700"
                  : "bg-white text-gray-700 border-gray-300 hover:border-green-500"
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: idx * 0.1 }}
            >
              Phase {idx + 1}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Tree Branch Structure */}
      <div className="relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={activePhase}
            className="bg-gradient-to-br from-black to-green-900 text-white rounded-xl shadow-xl p-8 md:p-10 transition-all duration-500"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            {/* Phase Header */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-10 border-b border-green-700/30 pb-6">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <h3 className="text-2xl md:text-3xl font-bold text-green-400 mb-2">
                  {course?.phases[activePhase].title}
                </h3>
                <p className="text-lg text-gray-300">
                  {course?.phases[activePhase]?.objective ?? ""}
                </p>
              </motion.div>
              <motion.div
                className="mt-4 md:mt-0 flex items-center bg-green-900/50 px-5 py-3 rounded-lg border border-green-500/30"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <FaCalendarAlt className="text-green-400 mr-3 text-xl" />
                <span className="font-medium text-white">
                  {course?.phases[activePhase].duration}
                </span>
              </motion.div>
            </div>

            {/* Tree Branch Content */}
            <div className="relative">
              {/* Center trunk line */}
              {/* <div className="absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 w-0.5 bg-green-500/40 z-0"></div> */}

              {/* Content Boxes */}
              <div className="relative z-10 space-y-16">
                {/* Topics Section - Left Side */}
                <div className="grid md:grid-cols-5 gap-4 items-center mb-12 md:mb-0">
                  {/* Content Box */}
                  <motion.div
                    className="md:col-span-2 bg-black/30 p-4 sm:p-6 rounded-lg border border-green-500/30 shadow-lg order-2 md:order-1 w-full"
                    initial={{ opacity: 0, x: -50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4, duration: 0.5 }}
                  >
                    <h4 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 flex items-center text-green-400">
                      <span className="inline-block p-1.5 sm:p-2 bg-green-900/70 rounded-full mr-2 sm:mr-3">
                        <FaLaptop className="text-green-400 text-sm sm:text-base" />
                      </span>
                      Topics Covered
                    </h4>
                    <motion.ul
                      className="space-y-2 sm:space-y-3 text-sm sm:text-base"
                      variants={staggerContainer}
                      initial="hidden"
                      animate="visible"
                    >
                      {course?.phases[activePhase].activities.map(
                        (topic: any, idx: any) => (
                          <motion.li
                            key={idx}
                            className="flex items-start"
                            variants={fadeInUp}
                            custom={idx}
                          >
                            <FaCheckCircle className="text-green-500 mt-1 mr-2 sm:mr-3 flex-shrink-0 text-xs sm:text-sm" />
                            <span>{topic?.title}</span>
                          </motion.li>
                        )
                      )}
                    </motion.ul>
                  </motion.div>

                  {/* Branch Connector */}
                  <div className="md:col-span-1 flex justify-center items-center h-full order-1 md:order-2">
                    {/* <motion.div
                        className="w-16 h-0.5 bg-green-500/70 hidden md:block"
                        initial={{ width: 0 }}
                        animate={{ width: "4rem" }}
                        transition={{ delay: 0.6, duration: 0.4 }}
                      ></motion.div>
                      <motion.div
                        className="h-10 sm:h-16 w-0.5 bg-green-500/70 block md:hidden"
                        initial={{ height: 0 }}
                        animate={{ height: "2.5rem" }}
                        transition={{ delay: 0.6, duration: 0.4 }}
                      ></motion.div> */}
                  </div>

                  {/* Center Node */}
                  <div className="md:col-span-2 flex justify-center order-1 md:order-3">
                    <motion.div
                      className="w-8 h-8 sm:w-12 sm:h-12 rounded-full bg-green-600 flex items-center justify-center border-2 sm:border-4 border-black"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.5, type: "spring" }}
                    >
                      <span className="font-bold text-white text-sm sm:text-base">
                        1
                      </span>
                    </motion.div>
                  </div>
                </div>

                {/* Projects Section - Right Side */}
                {course?.phases[activePhase]?.projects && (
                  <div className="grid md:grid-cols-5 gap-4 items-center">
                    {/* Center Node */}
                    <div className="md:col-span-2 flex justify-center order-1">
                      <motion.div
                        className="w-8 h-8 sm:w-12 sm:h-12 rounded-full bg-green-600 flex items-center justify-center border-2 sm:border-4 border-black"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.7, type: "spring" }}
                      >
                        <span className="font-bold text-white text-sm sm:text-base">
                          2
                        </span>
                      </motion.div>
                    </div>

                    {/* Branch Connector */}
                    <div className="md:col-span-1 flex justify-center items-center h-full order-1 md:order-2">
                      {/* <motion.div
                          className="w-16 h-0.5 bg-green-500/70 hidden md:block"
                          initial={{ width: 0 }}
                          animate={{ width: "4rem" }}
                          transition={{ delay: 0.8, duration: 0.4 }}
                        ></motion.div>
                        <motion.div
                          className="h-10 sm:h-16 w-0.5 bg-green-500/70 block md:hidden"
                          initial={{ height: 0 }}
                          animate={{ height: "2.5rem" }}
                          transition={{ delay: 0.8, duration: 0.4 }}
                        ></motion.div> */}
                    </div>

                    {/* Content Box */}
                    <motion.div
                      className="md:col-span-2 bg-black/30 p-4 sm:p-6 rounded-lg border border-green-500/30 shadow-lg order-2 md:order-3 w-full"
                      initial={{ opacity: 0, x: 50 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6, duration: 0.5 }}
                    >
                      <h4 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 flex items-center text-green-400">
                        <span className="inline-block p-1.5 sm:p-2 bg-green-900/70 rounded-full mr-2 sm:mr-3">
                          <FaProjectDiagram className="text-green-400 text-sm sm:text-base" />
                        </span>
                        Projects
                      </h4>
                      <motion.ul
                        className="space-y-2 sm:space-y-3 text-sm sm:text-base"
                        variants={staggerContainer}
                        initial="hidden"
                        animate="visible"
                      >
                        {course?.phases[activePhase]?.projects?.map(
                          (project: any, idx: any) => (
                            <motion.li
                              key={idx}
                              className="flex items-start"
                              variants={fadeInUp}
                              custom={idx}
                            >
                              <FaCheckCircle className="text-green-500 mt-1 mr-2 sm:mr-3 flex-shrink-0 text-xs sm:text-sm" />
                              <span>{project}</span>
                            </motion.li>
                          )
                        )}
                      </motion.ul>
                    </motion.div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Projects & Assessments Section */}
      {(course?.projects?.length > 0 || course?.assessment?.length > 0) && (
        <div className="mt-16">
          <div className="flex justify-center mb-12 relative">
            <div className="flex flex-wrap justify-center gap-2 sm:gap-3 md:gap-4">
              <motion.button
                className="relative z-10 px-4 sm:px-6 py-2 sm:py-2.5 rounded-full border-2 bg-green-700 text-white border-green-700 flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <FaClipboardCheck className="text-white" />
                Projects & Assessments
              </motion.button>
            </div>
          </div>

          <motion.div
            className="bg-gradient-to-br from-black to-green-900 text-white rounded-xl shadow-xl p-8 md:p-10 transition-all duration-500"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col md:flex-row gap-8">
              {/* Projects Column */}
              {course?.projects?.length > 0 && (
                <div className="w-full md:w-1/2">
                  <div className="flex flex-col justify-between items-start mb-6 border-b border-green-700/30 pb-4">
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                      className="flex items-center gap-3"
                    >
                      <span className="inline-block p-2 bg-green-900/70 rounded-full">
                        <FaProjectDiagram className="text-green-400 text-xl" />
                      </span>
                      <h3 className="text-xl md:text-2xl font-bold text-green-400 mb-0">
                        Course Projects
                      </h3>
                    </motion.div>
                  </div>

                  <motion.div
                    className="bg-black/30 p-4 sm:p-6 rounded-lg border border-green-500/30 shadow-lg"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <motion.ul
                      className="space-y-2 sm:space-y-3 text-sm sm:text-base"
                      initial="hidden"
                      animate="visible"
                      variants={staggerContainer}
                    >
                      {course?.projects?.map((project: any, idx: number) => (
                        <motion.li
                          key={idx}
                          className="flex items-start"
                          variants={fadeInUp}
                          custom={idx}
                        >
                          <FaCheckCircle className="text-green-500 mt-1 mr-2 sm:mr-3 flex-shrink-0 text-xs sm:text-sm" />
                          <div className="flex flex-col">
                            <span>{project?.title}</span>
                            <span className="text-gray-400 text-xs sm:text-sm">
                              {project?.description}
                            </span>
                          </div>
                        </motion.li>
                      ))}
                    </motion.ul>
                  </motion.div>
                </div>
              )}

              {/* Assessments Column */}
              {course?.assessment?.length > 0 && (
                <div className="w-full md:w-1/2">
                  <div className="flex flex-col justify-between items-start mb-6 border-b border-green-700/30 pb-4">
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                      className="flex items-center gap-3"
                    >
                      <span className="inline-block p-2 bg-green-900/70 rounded-full">
                        <FaClipboardList className="text-green-400 text-xl" />
                      </span>
                      <h3 className="text-xl md:text-2xl font-bold text-green-400 mb-0">
                        Course Assessments
                      </h3>
                    </motion.div>
                  </div>

                  <motion.div
                    className="bg-black/30 p-4 sm:p-6 rounded-lg border border-green-500/30 shadow-lg"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <motion.ul
                      className="space-y-2 sm:space-y-3 text-sm sm:text-base"
                      initial="hidden"
                      animate="visible"
                      variants={staggerContainer}
                    >
                      {course?.assessment?.map(
                        (assessment: any, idx: number) => (
                          <motion.li
                            key={idx}
                            className="flex items-start"
                            variants={fadeInUp}
                            custom={idx}
                          >
                            <FaCheckCircle className="text-green-500 mt-1 mr-2 sm:mr-3 flex-shrink-0 text-xs sm:text-sm" />
                            <div className="flex flex-col">
                              <div className="flex items-center">
                                <span className="font-medium">
                                  {assessment?.title}
                                </span>
                                {assessment?.weight && (
                                  <span className="ml-2 text-xs bg-green-900 text-green-300 px-2 py-0.5 rounded-full">
                                    {assessment.weight}
                                  </span>
                                )}
                              </div>
                              {assessment?.description && (
                                <span className="text-gray-400 text-xs sm:text-sm">
                                  {assessment.description}
                                </span>
                              )}
                            </div>
                          </motion.li>
                        )
                      )}
                    </motion.ul>
                  </motion.div>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </div>
  </motion.section>
);

export default CurriculumContent;
