import React from "react";

interface ImageSectionProps {
  leftImage?: string;
  rightImage?: string;
  imageAlt?: string;
  title?: string;
  description?: string | React.ReactNode;
}

const ImageSection = ({
  leftImage,
  rightImage,
  imageAlt,
  title,
  description,
}: ImageSectionProps) => {
  return (
    <div className="flex flex-col md:flex-row items-center justify-between gap-3 sm:gap-4 md:gap-8 py-4 sm:py-6 md:py-12 px-3 sm:px-4 hover:bg-gradient-to-r from-green-50 to-gray-50 transition-all duration-300 rounded-xl sm:rounded-2xl md:rounded-3xl">
      {leftImage && (
        <>
          <div className="w-full md:w-1/2 flex justify-center transform hover:rotate-2 sm:hover:rotate-3 transition-transform duration-300 mb-3 sm:mb-4 md:mb-0">
            <img src={leftImage} alt={imageAlt} className="w-1/2 sm:w-2/3 md:w-1/3 rounded-full shadow-md sm:shadow-xl hover:shadow-lg sm:hover:shadow-2xl border border-green-100 sm:border-2" />
          </div>
          <div className="w-full md:w-1/2 text-center md:text-left">
            {title && (
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 sm:mb-3 md:mb-4 bg-gradient-to-r from-green-700 to-green-900 text-transparent bg-clip-text">
                {title} 🌿
              </h2>
            )}
            <div className="text-gray-700 hover:text-black transition-colors duration-300">
              {description}
            </div>
          </div>
        </>
      )}
      {rightImage && (
        <>
          <div className="w-full md:w-1/2 text-center md:text-left mb-3 sm:mb-4 md:mb-0">
            {title && (
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 sm:mb-3 md:mb-4 bg-gradient-to-r from-green-700 to-green-900 text-transparent bg-clip-text">
                {title} 🌿
              </h2>
            )}
            <div className="text-gray-700 hover:text-black transition-colors duration-300">
              {description}
            </div>
          </div>
          <div className="w-full md:w-1/2 flex justify-center transform hover:-rotate-2 sm:hover:-rotate-3 transition-transform duration-300">
            <img
              src={rightImage}
              alt={imageAlt}
              className="w-1/2 sm:w-2/3 md:w-1/3 rounded-full shadow-md sm:shadow-xl hover:shadow-lg sm:hover:shadow-2xl border border-green-100 sm:border-2"
            />
          </div>
        </>
      )}
    </div>
  );
};

export default ImageSection;
