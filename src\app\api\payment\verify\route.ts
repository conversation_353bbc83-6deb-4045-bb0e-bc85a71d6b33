import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import crypto from "crypto";
import { connectDB } from "../../../../backend/lib/mongodb";
import { Payment, Course } from "../../../../backend/models";
import Plan from "../../../../backend/models/Plan";
import { env } from "../../../../backend/config/getEnv";

const RAZORPAY_KEY_SECRET = env.RAZORPAY_KEY_SECRET;

// Validation schema for payment verification
const verifySchema = z.object({
  razorpay_order_id: z.string(),
  razorpay_payment_id: z.string(),
  razorpay_signature: z.string(),
  courseId: z.string(),
  planId: z.string(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string(),
  amount: z.number(),
});

export async function POST(req: NextRequest) {
  try {
    if (!RAZORPAY_KEY_SECRET) {
      return NextResponse.json(
        { success: false, message: "Payment gateway not configured" },
        { status: 500 }
      );
    }

    await connectDB();
    const body = await req.json();

    // Validate request body
    const validationResult = verifySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation error",
          errors: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      courseId,
      planId,
      name,
      email,
      phone,
      amount,
    } = validationResult.data;

    // Verify signature
    const hmac = crypto.createHmac("sha256", RAZORPAY_KEY_SECRET);
    hmac.update(`${razorpay_order_id}|${razorpay_payment_id}`);
    const generatedSignature = hmac.digest("hex");

    if (generatedSignature !== razorpay_signature) {
      return NextResponse.json(
        { success: false, message: "Invalid payment signature" },
        { status: 400 }
      );
    }

    // Verify plan exists and is active
    const plan = await Plan.findOne({ planId, isActive: true });
    if (!plan) {
      return NextResponse.json(
        { success: false, message: "Invalid or inactive plan" },
        { status: 400 }
      );
    }

    // Verify amount matches plan amount
    if (amount !== plan.pricing.totalAmount) {
      return NextResponse.json(
        { success: false, message: "Payment amount does not match plan price" },
        { status: 400 }
      );
    }

    // Create payment record
    await Payment.create({
      name,
      email,
      phone,
      courseId,
      planId,
      orderId: razorpay_order_id,
      paymentId: razorpay_payment_id,
      amount: amount,
      currency: plan.pricing.currency,
      status: "completed",
    });

    // Get course details
    const course = await Course.findById(courseId).select("title slug");

    return NextResponse.json(
      {
        success: true,
        message: "Payment verified successfully",
        course: {
          title: course?.title,
          slug: course?.slug,
        },
        planId: plan.planId,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error verifying payment:", error);
    return NextResponse.json(
      { success: false, message: "Failed to verify payment" },
      { status: 500 }
    );
  }
}
