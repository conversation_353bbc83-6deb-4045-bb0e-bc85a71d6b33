import { motion } from 'framer-motion';
import ScrollReveal from "@/components/Common/ScrollReveal";

const staggerContainer = {
  hidden: {},
  visible: {
    transition: { staggerChildren: 0.1 }
  }
};

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

interface Resource {
  title: string;
  description: string;
  link: string;
}

interface Props {
  course?: {
    resources: Resource[];
  };
}

const ToolsContent: React.FC<Props> = ({ course }) => (
  <motion.section
    className="max-w-6xl mx-auto px-6 py-16"
    initial={{ opacity: 0 }}
    whileInView={{ opacity: 1 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true, amount: 0.1 }}
  >
    <ScrollReveal>
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-700 to-black">
          Tools & Resources
        </h2>
        <div className="h-1 w-24 bg-gradient-to-r from-green-500 to-green-700 mx-auto my-6"></div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          {`You'll get hands-on experience with industry-standard tools used
            by marketing professionals worldwide.`}
        </p>
      </div>
    </ScrollReveal>

    <motion.div
      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6"
      variants={staggerContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      {course?.resources.map((resource: any, idx: any) => (
        <motion.div
          key={idx}
          className="bg-gradient-to-br from-black to-green-900 text-white rounded-lg shadow-xl p-4 sm:p-6 border border-green-500/20"
          variants={fadeInUp}
          custom={idx}
          whileHover={{
            y: -10,
            boxShadow:
              "0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1)",
            borderColor: "rgba(74, 222, 128, 0.4)",
          }}
          transition={{ type: "spring", stiffness: 300, damping: 15 }}
        >
          <motion.div
            className="flex items-center mb-3 sm:mb-4"
            initial={{ x: -20, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 + idx * 0.1 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="bg-green-900 p-2 sm:p-3 rounded-full border border-green-500/30"
              whileHover={{ rotate: 10, scale: 1.1 }}
            >
              {/* <FaTools className="text-green-400 text-base sm:text-xl" /> */}
              <img
                src={`https://www.google.com/s2/favicons?sz=64&domain=${resource?.link}`}
                alt={resource.title}
                className="w-12 h-12 sm:w-8 sm:h-8 rounded-full object-cover"
              />
            </motion.div>
            <h3 className="ml-2 sm:ml-3 text-base sm:text-lg font-bold text-green-400">
              {resource.title}
            </h3>
          </motion.div>
          <p className="text-gray-300 text-sm sm:text-base">
            {resource.description}
          </p>
        </motion.div>
      ))}
    </motion.div>

    {/* <motion.div
        className="bg-gradient-to-br from-green-900 to-black text-white rounded-xl p-6 sm:p-8 md:p-10 mt-12 sm:mt-16 shadow-xl border border-green-500/20"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7, delay: 0.3 }}
        viewport={{ once: true, amount: 0.1 }}
      >
        <motion.h3
          className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-green-400 flex items-center"
          initial={{ x: -20, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          viewport={{ once: true }}
        >
          <FaGraduationCap className="mr-2 sm:mr-3 text-xl sm:text-2xl" />
          Learning Resources
        </motion.h3>

        <motion.div
          className="grid sm:grid-cols-2 gap-4 sm:gap-6"
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {course?.resources.map((resource: any, idx: any) => (
            <motion.div
              className="bg-black/30 p-4 sm:p-6 rounded-lg border border-green-500/20 shadow-lg"
              key={idx}
              variants={fadeInUp}
              custom={idx}
              whileHover={{
                scale: 1.02,
                boxShadow:
                  "0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1)",
                borderColor: "rgba(74, 222, 128, 0.3)",
              }}
            >
              <h4 className="font-bold text-base sm:text-lg mb-2 sm:mb-3 text-green-400">
                {resource?.title}
              </h4>
              <p className="text-gray-300 text-sm sm:text-base">
                {resource?.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </motion.div> */}
  </motion.section>
);

export default ToolsContent;
