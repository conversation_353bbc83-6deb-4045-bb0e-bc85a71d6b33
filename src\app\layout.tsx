import type { Metada<PERSON> } from "next";
import "./globals.css";
import { Navbar } from "@/components/Navbar";
import Footer from "@/components/Common/Footer";
import PageTransition from "@/components/Common/PageTransition";
import ScrollProgressBar from "@/components/Common/ScrollProgressBar";

export const metadata: Metadata = {
  title: "Grow Grid",
  description: "Generated by create next app",
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://www.growgrid.co.in'),
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={``}>
        <ScrollProgressBar color="#16a34a" height={4} />
        <Navbar />
        <PageTransition>
          <div>{children}</div>
        </PageTransition>
        <Footer />
      </body>
    </html>
  );
}
