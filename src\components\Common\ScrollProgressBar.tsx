"use client";

import React from "react";
import { motion, useScroll, useSpring } from "framer-motion";

interface ScrollProgressBarProps {
  color?: string;
  height?: number;
  zIndex?: number;
  position?: "top" | "bottom";
  showPercentage?: boolean;
}

const ScrollProgressBar: React.FC<ScrollProgressBarProps> = ({
  color = "#16a34a", // green-600
  height = 4,
  zIndex = 50,
  position = "top",
  showPercentage = false,
}) => {
  const { scrollYProgress } = useScroll();
  
  // Add spring physics for smoother progress
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001,
  });
  
  // Position styles
  const positionStyles = {
    top: { top: 0 },
    bottom: { bottom: 0 },
  };

  return (
    <>
      <motion.div
        className="fixed left-0 right-0"
        style={{
          ...positionStyles[position],
          height,
          backgroundColor: color,
          transformOrigin: "0%",
          scaleX,
          zIndex,
        }}
      />
      
      {showPercentage && (
        <motion.div
          className="fixed right-4 bg-white rounded-full shadow-md px-2 py-1 text-xs font-bold"
          style={{
            ...positionStyles[position],
            [position]: height + 8,
            zIndex,
            color,
          }}
        >
          <motion.span>
            {scrollYProgress.get() ? Math.round(scrollYProgress.get() * 100) : 0}%
          </motion.span>
        </motion.div>
      )}
    </>
  );
};

export default ScrollProgressBar;
