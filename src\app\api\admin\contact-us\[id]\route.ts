import { NextRequest, NextResponse } from "next/server";
import { Contact } from "../../../../../backend/models";
import { connectDB } from "../../../../../backend/lib/mongodb";
import { Params } from "../../../../../backend/utils/helper";

export async function GET(
  Request: NextRequest,
  { params }: { params: Params }
) {
  try {
    await connectDB();
    const id = (await params).id;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Contact ID is required" },
        { status: 400 }
      );
    }

    const contact = await Contact.findById(id, { __v: 0 });
    if (!contact) {
      return NextResponse.json(
        { success: false, message: "Contact not found" },
        { status: 404 }
      );
    }
    return NextResponse.json({ success: true, data: contact });
  } catch (error: unknown) {
    console.log(error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: errorMessage.includes("timeout") ? 504 : 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    await connectDB();
    const id = (await params).id;
    if (!id) {
      return NextResponse.json(
        { success: false, message: "Contact ID is required" },
        { status: 400 }
      );
    }

    const contact = await Contact.findByIdAndDelete(id);
    if (!contact) {
      return NextResponse.json(
        { success: false, message: "Contact not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Contact deleted successfully",
    });
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 400 }
    );
  }
}
