"use client";

import React from "react";
import { motion } from "framer-motion";

interface TextRevealProps {
  text: string;
  className?: string;
  delay?: number;
  duration?: number;
  staggerChildren?: number;
  once?: boolean;
  threshold?: number;
  type?: "words" | "chars" | "lines";
}

const TextReveal: React.FC<TextRevealProps> = ({
  text,
  className = "",
  delay = 0,
  duration = 0.5,
  staggerChildren = 0.03,
  once = true,
  threshold = 0.1,
  type = "words",
}) => {
  // Split text based on type
  const splitText = () => {
    switch (type) {
      case "chars":
        return text.split("");
      case "lines":
        return text.split("\\n");
      case "words":
      default:
        return text.split(" ");
    }
  };
  
  const items = splitText();
  
  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    visible: (i = 1) => ({
      opacity: 1,
      transition: {
        staggerChildren,
        delayChildren: delay * i,
      },
    }),
  };
  
  const child = {
    hidden: {
      opacity: 0,
      y: 20,
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 100,
      },
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 100,
        duration,
      },
    },
  };

  return (
    <motion.div
      className={`inline-block ${className}`}
      variants={container}
      initial="hidden"
      whileInView="visible"
      viewport={{ once, amount: threshold }}
    >
      {items.map((item, index) => (
        <motion.span
          key={index}
          variants={child}
          className="inline-block"
          style={{ 
            marginRight: type === "words" ? "0.25em" : type === "chars" ? "0" : "0",
            whiteSpace: type === "lines" ? "pre" : "normal"
          }}
        >
          {item}
        </motion.span>
      ))}
    </motion.div>
  );
};

export default TextReveal;
