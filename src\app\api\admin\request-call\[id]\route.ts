import { NextResponse } from "next/server";
import { connectDB } from "@/backend/lib/mongodb";
import RequestCall from "@/backend/models/RequestCall"; // Adjust the import path as needed
import { Params, RequestCallSchema } from "@/backend/utils/helper";

export async function GET(request: Request, { params }: { params: Params }) {
  try {
    await connectDB();

    // Check if we're getting a specific course by ID
    const id = (await params).id;
    if (!id) {
      return NextResponse.json(
        { success: false, message: "Course ID is required" },
        { status: 400 }
      );
    }

    // Create new request call entry
    const newRequestCall = await RequestCall.findById(id);
    return NextResponse.json(
      { success: true, data: newRequestCall },
      { status: 201 }
    );
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";


    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: errorMessage.includes("timeout") ? 504 : 500 }
    );
  }
}
