import { NextResponse } from "next/server";
import { connectDB } from "@/backend/lib/mongodb";
import RequestCall from "@/backend/models/RequestCall"; // Adjust the import path as needed
import { RequestCallSchema } from "@/backend/utils/helper";

export async function GET(request: Request) {
  try {
    await connectDB();

    // Create new request call entry
    const newRequestCall = await RequestCall.find({}).sort({ createdAt: -1 });

    return NextResponse.json(
      { success: true, data: newRequestCall },
      { status: 201 }
    );
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";

    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: errorMessage.includes("timeout") ? 504 : 500 }
    );
  }
}
