"use client";

import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { slugify } from "@/utils/helper";

// Separate internship and capsule courses with their icons
const internshipCourses = [
  {
    name: "End to End Full Stack Web Development",
    icon: "/assets/icons/nav/full-stack.png",
  },
  {
    name: "Applied Data Science Program",
    icon: "/assets/icons/nav/data-science.png",
  },
  {
    name: "Cyber Security & Risk Management",
    icon: "/assets/icons/nav/cyber-security.png",
  },
  {
    name: "Cloud Architecture and Computing",
    icon: "/assets/icons/nav/cloud-architecture.png",
  },
  {
    name: "VLSI Design Fundamentals with AutoCAD",
    icon: "/assets/icons/nav/vlsi-autoCAD.png",
  },
  {
    name: "UI/UX for Digital Transformation",
    icon: "/assets/icons/nav/ui-ux.png",
  },
  {
    name: "AI-Powered Machine Learning",
    icon: "/assets/icons/nav/machine-leaning.png",
  },
  {
    name: "Strategic Human Resources",
    icon: "/assets/icons/nav/hr.png",
  },
  {
    name: "Digital Marketing for Business Transformation",
    icon: "/assets/icons/nav/digital-marketing.png",
  },
];

const capsuleCourses = [
  { name: "Core Concepts of C++", icon: "/assets/icons/nav/c.png" },
  { name: "Core Concepts of Java", icon: "/assets/icons/nav/java.png" },
  { name: "Artificial Intelligence", icon: "/assets/icons/nav/ai.png" },
  { name: "Data Structures & Algorithms", icon: "/assets/icons/nav/data-structure.png" },
  { name: "Machine Learning with Data Science", icon: "/assets/icons/nav/machine-leaning.png" },
];

const InfinityScroll = () => {
  // We'll keep the responsive detection for future use
  const [isMobile, setIsMobile] = useState(false);

  // Handle responsive layout detection
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    // Set initial values
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Responsive adjustments based on screen size
  const scrollSpeed = isMobile ? 20 : 40; // Faster on mobile for better UX

  // Duplicate the arrays to create a seamless loop effect
  const duplicatedInternships = [...internshipCourses, ...internshipCourses];
  const duplicatedCapsules = [...capsuleCourses, ...capsuleCourses];

  return (
    <div className="w-full xs:w-11/12 sm:w-5/6 md:w-4/5 lg:w-3/4 xl:w-2/3 mx-auto rounded-xl sm:rounded-2xl py-6 sm:py-8 md:py-12 bg-gradient-to-br from-green-50 to-green-100 overflow-hidden relative">
      <div className="mb-4 sm:mb-6 md:mb-8 text-center px-4">
        <h2 className="text-xl xs:text-2xl sm:text-3xl md:text-4xl font-bold">
          <div className="text-black">
            Upskill and transform your career in latest
          </div>{" "}
          <span className="text-green-600">technologies</span>{" "}
          <span className="text-black">and</span>{" "}
          <span className="text-green-600">domains</span>
        </h2>
        <div className="h-1 w-16 sm:w-20 bg-green-600 mx-auto my-2 sm:my-4"></div>
      </div>

      {/* Infinity scroll container */}
      <div className="w-full overflow-hidden py-2 sm:py-4">
        {/* First row - Internships (right to left) */}
        <div className="mb-2 sm:mb-4">
          <motion.div
            className="flex whitespace-nowrap mb-4 sm:mb-6"
            animate={{
              x: [-(isMobile ? 180 : 250) * internshipCourses.length, 0], // Adjusted for full text display
            }}
            transition={{
              x: {
                repeat: Infinity,
                repeatType: "loop",
                duration: scrollSpeed, // Using responsive speed
                ease: "linear",
              },
            }}
          >
            {duplicatedInternships.map((course, index) => (
              <Link
                key={`internship-${index}`}
                href={`/courses/${slugify(course.name)}`}
                className="inline-flex mx-2 sm:mx-4 px-3 sm:px-5 py-1.5 sm:py-2.5 bg-white rounded-2xl shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105 hover:bg-green-50 border border-green-200 group"
              >
                <div className="flex items-center">
                  <img
                    src={course.icon}
                    alt={course.name}
                    className="w-4 h-4 xs:w-5 xs:h-5 mr-1.5 xs:mr-2 object-contain flex-shrink-0"
                  />
                  <span className="text-gray-800 text-xs xs:text-sm sm:text-base font-medium group-hover:text-green-700">
                    {course.name}
                  </span>
                </div>
              </Link>
            ))}
          </motion.div>
        </div>

        {/* Second row - Capsules (left to right) */}
        <div>
          <motion.div
            className="flex whitespace-nowrap"
            animate={{
              x: [0, -(isMobile ? 180 : 250) * capsuleCourses.length], // Adjusted for full text display
            }}
            transition={{
              x: {
                repeat: Infinity,
                repeatType: "loop",
                duration: scrollSpeed * 0.9, // Slightly different speed for visual interest
                ease: "linear",
              },
            }}
          >
            {duplicatedCapsules.map((course, index) => (
              <Link
                key={`capsule-${index}`}
                href={`/courses/${slugify(course.name)}`}
                className="inline-flex mx-2 sm:mx-4 px-3 sm:px-5 py-1.5 sm:py-2.5 bg-gradient-to-r from-green-50 to-white rounded-2xl shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105 hover:bg-green-100 border border-green-300 group"
              >
                <div className="flex items-center">
                  <img
                    src={course.icon}
                    alt={course.name}
                    className="w-4 h-4 xs:w-5 xs:h-5 mr-1.5 xs:mr-2 object-contain flex-shrink-0"
                  />
                  <span className="text-gray-800 text-xs xs:text-sm sm:text-base font-medium group-hover:text-green-700">
                    {course.name}
                  </span>
                </div>
              </Link>
            ))}
          </motion.div>
        </div>
      </div>

      {/* Add a subtle gradient overlay to create fade effect on the sides */}
      <div className="absolute left-0 top-0 h-full w-8 xs:w-12 sm:w-16 bg-gradient-to-r from-green-50 to-transparent pointer-events-none"></div>
      <div className="absolute right-0 top-0 h-full w-8 xs:w-12 sm:w-16 bg-gradient-to-l from-green-50 to-transparent pointer-events-none"></div>
    </div>
  );
};

export default InfinityScroll;
