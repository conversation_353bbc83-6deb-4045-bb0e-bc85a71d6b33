"use client";

import React from "react";
import { motion } from "framer-motion";
import { popIn, fadeInUp } from "@/utils/animations";

type InfoCardProps = {
  title?: string;
  description?: string;
  icon?: React.ReactNode | null;
  delay?: number;
};

const InfoCard = ({
  title = "",
  description = "",
  icon = null,
  delay = 0,
}: InfoCardProps) => {
  return (
    <motion.div
      className="w-full p-3 sm:p-4 md:p-6 rounded-2xl sm:rounded-3xl border-2 hover:bg-gradient-to-r from-purple-50 to-pink-50 hover:border-purple-200 hover:shadow-xl sm:hover:shadow-2xl flex flex-col items-center justify-center h-full"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.3 }}
      variants={fadeInUp}
      transition={{ delay: delay * 0.1 }}
      whileHover={{ y: -5, scale: 1.01 }}
    >
      <motion.div
        className="w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 bg-gradient-to-br from-green-800 to-green-500 rounded-xl sm:rounded-2xl flex items-center justify-center mb-2 sm:mb-3 md:mb-4"
        whileHover={{ rotate: 8, scale: 1.05 }}
        transition={{ type: "spring", stiffness: 300, damping: 10 }}
        variants={popIn}
      >
        {icon ?? (
          <span className="text-xl sm:text-2xl md:text-3xl">🎨</span>
        )}
      </motion.div>

      <motion.h2
        className="text-base sm:text-lg md:text-xl font-bold text-gray-800 mb-1 sm:mb-2 truncate w-full text-center"
        variants={fadeInUp}
        transition={{ delay: 0.1 }}
      >
        {title}
      </motion.h2>

      <motion.p
        className="text-gray-600 text-center text-[10px] xs:text-xs sm:text-sm hover:text-gray-800"
        variants={fadeInUp}
        transition={{ delay: 0.2 }}
      >
        {description}
      </motion.p>
    </motion.div>
  );
};

export default InfoCard;
