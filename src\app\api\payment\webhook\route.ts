import { NextRequest, NextResponse } from "next/server";
import crypto from "crypto";
import { connectDB } from "../../../../backend/lib/mongodb";
import { Payment, Course, User } from "../../../../backend/models";
import { env } from "../../../../backend/config/getEnv";

const RAZORPAY_WEBHOOK_SECRET = env.RAZORPAY_WEBHOOK_SECRET as string;

export async function POST(req: NextRequest) {
  try {
    // Verify webhook signature
    const signature = req.headers.get("x-razorpay-signature");
    if (!signature) {
      return NextResponse.json(
        { success: false, message: "Missing signature" },
        { status: 400 }
      );
    }

    // Get the raw request body as text
    const rawBody = await req.text();
    
    // Verify signature
    const expectedSignature = crypto
      .createHmac("sha256", RAZORPAY_WEBHOOK_SECRET)
      .update(rawBody)
      .digest("hex");
    
    if (expectedSignature !== signature) {
      console.error("Invalid webhook signature");
      return NextResponse.json(
        { success: false, message: "Invalid signature" },
        { status: 400 }
      );
    }

    // Parse the webhook payload
    const payload = JSON.parse(rawBody);
    const event = payload.event;
    
    console.log(`Processing webhook event: ${event}`);
    
    await connectDB();

    // Handle different webhook events
    switch (event) {
      case "payment.authorized":
        await handlePaymentAuthorized(payload);
        break;
        
      case "payment.captured":
        await handlePaymentCaptured(payload);
        break;
        
      case "payment.failed":
        await handlePaymentFailed(payload);
        break;
        
      case "refund.created":
        await handleRefundCreated(payload);
        break;
        
      default:
        console.log(`Unhandled webhook event: ${event}`);
    }

    return NextResponse.json(
      { success: true, message: "Webhook processed successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Webhook processing error:", error);
    return NextResponse.json(
      { success: false, message: "Failed to process webhook" },
      { status: 500 }
    );
  }
}

// Handler functions for different webhook events
async function handlePaymentAuthorized(payload: any) {
  const paymentEntity = payload.payload.payment.entity;
  const orderId = paymentEntity.order_id;
  const notes = paymentEntity.notes || {};
  
  console.log(`Payment authorized for order: ${orderId}`);
  
  // Update payment status to authorized
  await Payment.findOneAndUpdate(
    { orderId },
    { 
      status: "authorized",
      paymentId: paymentEntity.id,
      paymentMethod: paymentEntity.method
    },
    { upsert: false }
  );
}

async function handlePaymentCaptured(payload: any) {
  const paymentEntity = payload.payload.payment.entity;
  const orderId = paymentEntity.order_id;
  const notes = paymentEntity.notes || {};
  
  console.log(`Payment captured for order: ${orderId}`);
  
  // Get payment details
  const payment = await Payment.findOne({ orderId });
  
  if (!payment) {
    console.error(`Payment record not found for order: ${orderId}`);
    return;
  }
  
  // Update payment status to completed
  await Payment.findOneAndUpdate(
    { orderId },
    { 
      status: "completed",
      paymentId: paymentEntity.id,
      paymentMethod: paymentEntity.method
    }
  );
  
  // Additional actions after successful payment
  // For example, grant course access, send confirmation email, etc.
  try {
    // Grant course access logic here
    console.log(`Granting access to course ${payment.courseId} for user ${payment.email}`);
    
    // You might want to add the user to the course's enrolled users list
    // or create enrollment records in your database
  } catch (error) {
    console.error("Error processing post-payment actions:", error);
  }
}

async function handlePaymentFailed(payload: any) {
  const paymentEntity = payload.payload.payment.entity;
  const orderId = paymentEntity.order_id;
  
  console.log(`Payment failed for order: ${orderId}`);
  
  // Update payment status to failed
  await Payment.findOneAndUpdate(
    { orderId },
    { 
      status: "failed",
      paymentId: paymentEntity.id,
      failureReason: paymentEntity.error_description || paymentEntity.error_code
    },
    { upsert: false }
  );
}

async function handleRefundCreated(payload: any) {
  const refundEntity = payload.payload.refund.entity;
  const paymentId = refundEntity.payment_id;
  
  console.log(`Refund created for payment: ${paymentId}`);
  
  // Update payment status to refunded
  await Payment.findOneAndUpdate(
    { paymentId },
    { 
      status: "refunded",
      refundId: refundEntity.id,
      refundAmount: refundEntity.amount / 100,
      refundReason: refundEntity.notes?.reason || "Refund initiated"
    }
  );
}