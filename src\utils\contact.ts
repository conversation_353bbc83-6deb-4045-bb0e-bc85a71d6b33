import { z } from "zod";

export interface ContactData {
    name: string;
    email: string;
    message: string;
    subject: string;
    phone?: string;
  }


export const contactSchema = z.object({
    name: z.string().min(2, "Name must be at least 2 characters"),
    email: z.string().email("Invalid email address"),
    phone: z.string().optional(),
    subject: z.string().min(3, "Subject must be at least 3 characters"),
    message: z.string().min(10, "Message must be at least 10 characters"),
  });
