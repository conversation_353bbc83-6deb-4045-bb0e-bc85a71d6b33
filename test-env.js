// Simple script to test environment variables
// Run with: node test-env.js

// Try to load dotenv if available
try {
  require('dotenv').config();
  console.log('Loaded dotenv successfully');
} catch (error) {
  console.warn('Failed to load dotenv, continuing without it');
}

console.log('=== Environment Variables Test ===');
console.log('Note: NODE_ENV is managed by Next.js and cannot be set in next.config.ts');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('APP_ENV:', process.env.APP_ENV || '[NOT SET]');
console.log('Effective environment:', process.env.APP_ENV || process.env.NODE_ENV || 'development');
console.log('DB_PRODUCTION:', process.env.DB_PRODUCTION ? '[SET]' : '[NOT SET]');
console.log('DB_TEST:', process.env.DB_TEST ? '[SET]' : '[NOT SET]');
console.log('DB_DEVELOPMENT:', process.env.DB_DEVELOPMENT ? '[SET]' : '[NOT SET]');
console.log('DB_LOCAL:', process.env.DB_LOCAL ? '[SET]' : '[NOT SET]');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? '[SET]' : '[NOT SET]');
console.log('=== End Test ===');
