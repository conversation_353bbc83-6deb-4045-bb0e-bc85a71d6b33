import { motion } from "framer-motion";
import Image from "next/image";
import { useState, useRef } from "react";
import { IoChevronBack, IoChevronForward } from "react-icons/io5";

interface Company {
  id: number;
  name: string;
  logo: string;
  website?: string;
}

const companies: Company[] = [
  {
    id: 1,
    name: "Google",
    logo: "https://upload.wikimedia.org/wikipedia/commons/2/2f/Google_2015_logo.svg",
    website: "https://google.com"
  },
  {
    id: 2,
    name: "Microsoft",
    logo: "https://upload.wikimedia.org/wikipedia/commons/9/96/Microsoft_logo_%282012%29.svg",
    website: "https://microsoft.com"
  },
  {
    id: 3,
    name: "Amazon",
    logo: "https://upload.wikimedia.org/wikipedia/commons/a/a9/Amazon_logo.svg",
    website: "https://amazon.com"
  },
  {
    id: 4,
    name: "<PERSON>",
    logo: "https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg",
    website: "https://apple.com"
  },
  {
    id: 5,
    name: "<PERSON><PERSON>",
    logo: "https://upload.wikimedia.org/wikipedia/commons/7/7b/Meta_Platforms_Inc._logo.svg",
    website: "https://meta.com"
  },
  {
    id: 6,
    name: "Netflix",
    logo: "https://upload.wikimedia.org/wikipedia/commons/0/08/Netflix_2015_logo.svg",
    website: "https://netflix.com"
  },
  {
    id: 7,
    name: "Tesla",
    logo: "https://upload.wikimedia.org/wikipedia/commons/b/bb/Tesla_T_symbol.svg",
    website: "https://tesla.com"
  },
  {
    id: 8,
    name: "Spotify",
    logo: "https://upload.wikimedia.org/wikipedia/commons/1/19/Spotify_logo_without_text.svg",
    website: "https://spotify.com"
  },
  {
    id: 9,
    name: "Adobe",
    logo: "https://upload.wikimedia.org/wikipedia/commons/7/7b/Adobe_Systems_logo_and_wordmark.svg",
    website: "https://adobe.com"
  },
  {
    id: 10,
    name: "IBM",
    logo: "https://upload.wikimedia.org/wikipedia/commons/5/51/IBM_logo.svg",
    website: "https://ibm.com"
  },
  {
    id: 11,
    name: "Oracle",
    logo: "https://upload.wikimedia.org/wikipedia/commons/5/50/Oracle_logo.svg",
    website: "https://oracle.com"
  },
  {
    id: 12,
    name: "Salesforce",
    logo: "https://upload.wikimedia.org/wikipedia/commons/f/f9/Salesforce.com_logo.svg",
    website: "https://salesforce.com"
  }
];

const CompanyCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const carouselRef = useRef<HTMLDivElement>(null);

  // Duplicate companies for seamless infinite scroll
  const duplicatedCompanies = [...companies, ...companies];
  const itemWidth = 160; // Width of each item including margin

  const handlePrevious = () => {
    setIsAutoPlaying(false);
    setCurrentIndex((prev) => {
      const newIndex = prev - 1;
      return newIndex < 0 ? companies.length - 1 : newIndex;
    });
    // Resume auto-play after 5 seconds
    setTimeout(() => setIsAutoPlaying(true), 5000);
  };

  const handleNext = () => {
    setIsAutoPlaying(false);
    setCurrentIndex((prev) => {
      const newIndex = prev + 1;
      return newIndex >= companies.length ? 0 : newIndex;
    });
    // Resume auto-play after 5 seconds
    setTimeout(() => setIsAutoPlaying(true), 5000);
  };

  return (
    <section className="py-16 bg-gray-50 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Trusted by Leading Companies
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Our graduates work at top companies worldwide, building innovative solutions 
            and driving technological advancement.
          </p>
        </motion.div>

        {/* Carousel Container */}
        <div className="relative">

          <motion.div
            ref={carouselRef}
            className="flex space-x-8 md:space-x-12"
            animate={isAutoPlaying ? {
              x: [0, -itemWidth * companies.length]
            } : {
              x: -currentIndex * itemWidth
            }}
            transition={isAutoPlaying ? {
              x: {
                repeat: Infinity,
                repeatType: "loop",
                duration: 30,
                ease: "linear",
              },
            } : {
              type: "spring",
              stiffness: 300,
              damping: 30
            }}
          >
            {duplicatedCompanies.map((company, index) => (
              <motion.div
                key={`${company.id}-${index}`}
                className="flex-shrink-0 w-32 h-20 md:w-40 md:h-24 flex items-center justify-center bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300 group cursor-pointer"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                onClick={() => company.website && window.open(company.website, '_blank')}
              >
                <div className="relative w-20 h-12 md:w-24 md:h-14">
                  <Image
                    src={company.logo}
                    alt={`${company.name} logo`}
                    fill
                    className="object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                    onError={(e) => {
                      // Fallback if logo doesn't load
                      e.currentTarget.style.display = 'none';
                      const parent = e.currentTarget.parentElement;
                      if (parent) {
                        parent.innerHTML = `<span class="text-gray-600 font-semibold text-sm">${company.name}</span>`;
                      }
                    }}
                  />
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Gradient Overlays */}
          <div className="absolute left-0 top-0 w-20 h-full bg-gradient-to-r from-gray-50 to-transparent pointer-events-none z-10"></div>
          <div className="absolute right-0 top-0 w-20 h-full bg-gradient-to-l from-gray-50 to-transparent pointer-events-none z-10"></div>
        </div>

        {/* Bottom Navigation Controls */}
        <div className="flex items-center justify-center mt-8 gap-4">
          {/* Navigation Buttons */}
          <button
            onClick={handlePrevious}
            className="bg-white hover:bg-gray-50 border border-gray-200 rounded-full p-3 shadow-lg transition-all duration-200 hover:shadow-xl group"
            aria-label="Previous companies"
          >
            <IoChevronBack className="w-5 h-5 text-gray-600 group-hover:text-green-600 transition-colors" />
          </button>

          {/* Pause/Play Indicator */}
          <div className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
            isAutoPlaying
              ? 'bg-green-100 text-green-700 border border-green-200'
              : 'bg-orange-100 text-orange-700 border border-orange-200'
          }`}>
            {isAutoPlaying ? '▶ Auto-playing' : '⏸ Paused'}
          </div>

          <button
            onClick={handleNext}
            className="bg-white hover:bg-gray-50 border border-gray-200 rounded-full p-3 shadow-lg transition-all duration-200 hover:shadow-xl group"
            aria-label="Next companies"
          >
            <IoChevronForward className="w-5 h-5 text-gray-600 group-hover:text-green-600 transition-colors" />
          </button>
        </div>

        {/* Stats Section */}
        <motion.div
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2">500+</div>
            <div className="text-gray-600">Companies Hiring</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2">95%</div>
            <div className="text-gray-600">Placement Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2">₹12L</div>
            <div className="text-gray-600">Average Package</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2">2000+</div>
            <div className="text-gray-600">Success Stories</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CompanyCarousel;
