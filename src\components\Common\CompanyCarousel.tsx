import { motion } from "framer-motion";
import Image from "next/image";
import { useState, useRef } from "react";
import { IoChevronBack, IoChevronForward } from "react-icons/io5";

interface Company {
  id: number;
  name: string;
  logo: string;
  website?: string;
}

const companies: Company[] = [
  {
    id: 1,
    name: "<PERSON> App",
    logo: "https://wise.com/public-resources/assets/logos/logo-square.svg",
    website: "https://wise.com",
  },
  {
    id: 2,
    name: "Interakt",
    logo: "https://images.crunchbase.com/image/upload/c_lpad,f_auto,q_auto:eco,dpr_1/erkxwhl1gd48v8qgd5be",
    website: "https://interakt.ai",
  },
  {
    id: 3,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    logo: "https://razorpay.com/assets/razorpay-logo.svg",
    website: "https://razorpay.com",
  },
  {
    id: 4,
    name: "Indeed",
    logo: "https://upload.wikimedia.org/wikipedia/commons/f/fc/Indeed_logo.svg",
    website: "https://indeed.com",
  },
  {
    id: 5,
    name: "LinkedIn",
    logo: "https://upload.wikimedia.org/wikipedia/commons/c/ca/LinkedIn_logo_initials.png",
    website: "https://linkedin.com",
  },
  {
    id: 6,
    name: "Naukri",
    logo: "https://static.naukimg.com/s/0/0/i/naukri-identity/naukri_gnb_logo.svg",
    website: "https://naukri.com",
  },
  {
    id: 7,
    name: "Wipro",
    logo: "https://upload.wikimedia.org/wikipedia/commons/a/a0/Wipro_Primary_Logo_Color_RGB.svg",
    website: "https://wipro.com",
  },
  {
    id: 8,
    name: "PWC",
    logo: "https://logos-world.net/wp-content/uploads/2020/09/PwC-Logo.png",
    website: "https://pwc.com",
  },
  {
    id: 9,
    name: "Amazon",
    logo: "https://upload.wikimedia.org/wikipedia/commons/a/a9/Amazon_logo.svg",
    website: "https://amazon.com",
  },
  {
    id: 10,
    name: "HCL Technologies",
    logo: "https://www.hcltech.com/themes/custom/hcltech/images/hcltech-new-logo.svg",
    website: "https://hcltech.com",
  },
  {
    id: 11,
    name: "Applify",
    logo: "https://media.licdn.com/dms/image/C4D0BAQHQf8Z8Z8Z8ZA/company-logo_200_200/0/1630511234567?e=2147483647&v=beta&t=8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8Z8",
    website: "https://applify.com",
  },
  {
    id: 12,
    name: "Google",
    logo: "https://upload.wikimedia.org/wikipedia/commons/2/2f/Google_2015_logo.svg",
    website: "https://google.com",
  },
  {
    id: 13,
    name: "GoDaddy",
    logo: "https://upload.wikimedia.org/wikipedia/commons/1/1d/GoDaddy_logo.svg",
    website: "https://godaddy.com",
  },
  {
    id: 14,
    name: "Meta",
    logo: "https://upload.wikimedia.org/wikipedia/commons/7/7b/Meta_Platforms_Inc._logo.svg",
    website: "https://meta.com",
  },
  {
    id: 15,
    name: "Intel",
    logo: "https://upload.wikimedia.org/wikipedia/commons/7/7d/Intel_logo_%282006-2020%29.svg",
    website: "https://intel.com",
  },
];

const CompanyCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const carouselRef = useRef<HTMLDivElement>(null);

  // Duplicate companies for seamless infinite scroll
  const duplicatedCompanies = [...companies, ...companies];
  const itemWidth = 160; // Width of each item including margin

  const handlePrevious = () => {
    setIsAutoPlaying(false);
    setCurrentIndex((prev) => {
      const newIndex = prev - 1;
      return newIndex < 0 ? companies.length - 1 : newIndex;
    });
    // Resume auto-play after 5 seconds
    setTimeout(() => setIsAutoPlaying(true), 5000);
  };

  const handleNext = () => {
    setIsAutoPlaying(false);
    setCurrentIndex((prev) => {
      const newIndex = prev + 1;
      return newIndex >= companies.length ? 0 : newIndex;
    });
    // Resume auto-play after 5 seconds
    setTimeout(() => setIsAutoPlaying(true), 5000);
  };

  return (
    <section className="py-8 bg-gray-50 overflow-hidden">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-3">
            Trusted by Leading Companies
          </h2>
          <p className="text-base text-gray-600 max-w-xl mx-auto">
            Our graduates work at top companies worldwide
          </p>
        </motion.div>

        {/* Carousel Container */}
        <div className="relative">
          <motion.div
            ref={carouselRef}
            className="flex space-x-4 md:space-x-6"
            animate={
              isAutoPlaying
                ? {
                    x: [0, -itemWidth * companies.length],
                  }
                : {
                    x: -currentIndex * itemWidth,
                  }
            }
            transition={
              isAutoPlaying
                ? {
                    x: {
                      repeat: Infinity,
                      repeatType: "loop",
                      duration: 25,
                      ease: "linear",
                    },
                  }
                : {
                    type: "spring",
                    stiffness: 300,
                    damping: 30,
                  }
            }
          >
            {duplicatedCompanies.map((company, index) => (
              <motion.div
                key={`${company.id}-${index}`}
                className="flex-shrink-0 w-36 h-20 md:w-44 md:h-28 flex items-center justify-center bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300 group cursor-pointer"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                onClick={() =>
                  company.website && window.open(company.website, "_blank")
                }
              >
                <div className="relative w-24 h-14 md:w-28 md:h-16">
                  <Image
                    src={company.logo}
                    alt={`${company.name} logo`}
                    fill
                    className="object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                    onError={(e) => {
                      // Fallback if logo doesn't load
                      e.currentTarget.style.display = "none";
                      const parent = e.currentTarget.parentElement;
                      if (parent) {
                        parent.innerHTML = `<span class="text-gray-600 font-semibold text-sm">${company.name}</span>`;
                      }
                    }}
                  />
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Gradient Overlays */}
          {/* <div className="absolute left-0 top-0 w-16 h-full bg-gradient-to-r from-gray-50 to-transparent pointer-events-none z-10"></div> */}
          {/* <div className="absolute right-0 top-0 w-16 h-full bg-gradient-to-l from-gray-50 to-transparent pointer-events-none z-10"></div> */}
        </div>

        {/* Bottom Navigation Controls */}
        <div className="flex items-center justify-center mt-6 gap-3">
          {/* Navigation Buttons */}
          <button
            onClick={handlePrevious}
            className="bg-white hover:bg-gray-50 border border-gray-200 rounded-full p-2 shadow-md transition-all duration-200 hover:shadow-lg group"
            aria-label="Previous companies"
          >
            <IoChevronBack className="w-4 h-4 text-gray-600 group-hover:text-green-600 transition-colors" />
          </button>

          {/* Pause/Play Indicator */}
          <div
            className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-300 ${
              isAutoPlaying
                ? "bg-green-100 text-green-700 border border-green-200"
                : "bg-orange-100 text-orange-700 border border-orange-200"
            }`}
          >
            {isAutoPlaying ? "▶ Auto" : "⏸ Paused"}
          </div>

          <button
            onClick={handleNext}
            className="bg-white hover:bg-gray-50 border border-gray-200 rounded-full p-2 shadow-md transition-all duration-200 hover:shadow-lg group"
            aria-label="Next companies"
          >
            <IoChevronForward className="w-4 h-4 text-gray-600 group-hover:text-green-600 transition-colors" />
          </button>
        </div>

        {/* Stats Section */}
        <motion.div
          className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <div className="text-center">
            <div className="text-xl md:text-2xl font-bold text-green-600 mb-1">
              500+
            </div>
            <div className="text-gray-600 text-sm">Companies</div>
          </div>
          <div className="text-center">
            <div className="text-xl md:text-2xl font-bold text-green-600 mb-1">
              95%
            </div>
            <div className="text-gray-600 text-sm">Placement</div>
          </div>
          <div className="text-center">
            <div className="text-xl md:text-2xl font-bold text-green-600 mb-1">
              ₹12L
            </div>
            <div className="text-gray-600 text-sm">Avg Package</div>
          </div>
          <div className="text-center">
            <div className="text-xl md:text-2xl font-bold text-green-600 mb-1">
              2000+
            </div>
            <div className="text-gray-600 text-sm">Students</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CompanyCarousel;
